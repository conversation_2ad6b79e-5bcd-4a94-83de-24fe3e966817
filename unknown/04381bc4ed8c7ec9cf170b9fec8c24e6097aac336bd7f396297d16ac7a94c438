# Global Accounts

Global Accounts in Malda are the first implementation of application specific wallets proposed by <PERSON><PERSON> in a blog post recently.&#x20;

The solution is fully wallet agnostic and after an initial set-up you will be able to execute transactions across multiple chains with a single signature - no need to switch and sign between chains.

Users can best access the protocol by setting up their global accounts in the protocol on top of their existing wallets. This means that users will deposit and receive funds in the wallets they have used previously, but they will have a global method of interacting with the Malda protocol, eliminating the friction of switching chains and paying gas fees

Under the hood of Global Accounts is a Delegator and a Delegate Smart Contract Account (SCA) per chain for the user. Users can select which chains they wish to have these SCAs set up within the Global Account.

Only the users' EOA can interact with the Delegate SCA, and the SCA can only interact with the Malda application:

* The initial Delegator SCA only has the user’s EOA as a signer, while the Delegate SCA has the user’s passkey as a signer.
* The EOA gives permission to the Delegator SCA to spend their tokens.
* The Delegator delegates the right to interact with Malda to the Delegate SCA via Passkey.
* The Malda application uses passkey sign-in and signatures to execute the desired transactions on any of the supported chains.
