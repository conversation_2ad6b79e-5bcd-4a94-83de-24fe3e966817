# Introduction

This repository contains the source for the onchain Solidity components required to run Malda, the first DeFi protocol built in the Risc Zero zkVM. These critical components serve multiple function within the Malda application:

* Secure protocol ledger onchain
* Execute UserOps leveraging proofs generated mwith the malda-zk-coprocessor component
* Provides an entry point for users from multiple underlying chains leveraging mTokenGateway contract
