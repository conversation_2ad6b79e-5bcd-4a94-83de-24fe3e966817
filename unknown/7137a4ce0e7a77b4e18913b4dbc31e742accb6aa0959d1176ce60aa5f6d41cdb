{"transactions": [{"hash": "0xb3437eef1319c74b40f0254d0624ed2b1230afcff5d74986a399790ad927c822", "transactionType": "CALL", "contractName": null, "contractAddress": "0x82e8399ae0f58a6f4dbccd459e1fae6d80dbe852", "function": "create(bytes32,bytes)", "arguments": ["0x6f9b5183e0dad8e05b819edff6721c55406b492f1d4bdcdddfee72e04f93b8bf", "0x6080604052348015600e575f5ffd5b506101248061001c5f395ff3fe6080604052348015600e575f5ffd5b5060043610603a575f3560e01c80633fb5c1cb14603e5780638381f58a14604f578063d09de08a146068575b5f5ffd5b604d6049366004607d565b5f55565b005b60565f5481565b60405190815260200160405180910390f35b604d5f805490806076836093565b9190505550565b5f60208284031215608c575f5ffd5b5035919050565b5f7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff820360e7577f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b506001019056fea264697066735822122028f8155e9f269d2de2d617fad9f7cbdf5ed56a78a3ccd930ae046c848fa775b264736f6c634300081b0033"], "transaction": {"from": "0x0000e6203db925dbfb9e3d650a80a1e2f4a78e94", "to": "0x82e8399ae0f58a6f4dbccd459e1fae6d80dbe852", "gas": "0x36734", "value": "0x0", "input": "0x5b37e1506f9b5183e0dad8e05b819edff6721c55406b492f1d4bdcdddfee72e04f93b8bf000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000001406080604052348015600e575f5ffd5b506101248061001c5f395ff3fe6080604052348015600e575f5ffd5b5060043610603a575f3560e01c80633fb5c1cb14603e5780638381f58a14604f578063d09de08a146068575b5f5ffd5b604d6049366004607d565b5f55565b005b60565f5481565b60405190815260200160405180910390f35b604d5f805490806076836093565b9190505550565b5f60208284031215608c575f5ffd5b5035919050565b5f7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff820360e7577f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b506001019056fea264697066735822122028f8155e9f269d2de2d617fad9f7cbdf5ed56a78a3ccd930ae046c848fa775b264736f6c634300081b0033", "nonce": "0x58", "chainId": "0xaa36a7"}, "additionalContracts": [{"transactionType": "CREATE2", "address": "0x796d56835c0f4d0824737f408c7bf1d9db321284", "initCode": "0x67363d3d37363d34f03d5260086018f3"}, {"transactionType": "CREATE", "address": "0x5b67f782c44188a89e599db28e4409e4966b0103", "initCode": "0x6080604052348015600e575f5ffd5b506101248061001c5f395ff3fe6080604052348015600e575f5ffd5b5060043610603a575f3560e01c80633fb5c1cb14603e5780638381f58a14604f578063d09de08a146068575b5f5ffd5b604d6049366004607d565b5f55565b005b60565f5481565b60405190815260200160405180910390f35b604d5f805490806076836093565b9190505550565b5f60208284031215608c575f5ffd5b5035919050565b5f7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff820360e7577f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b506001019056fea264697066735822122028f8155e9f269d2de2d617fad9f7cbdf5ed56a78a3ccd930ae046c848fa775b264736f6c634300081b0033"}], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0x1bbf7c3", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0xb3437eef1319c74b40f0254d0624ed2b1230afcff5d74986a399790ad927c822", "transactionIndex": "0xb4", "blockHash": "0x3c8f22064f73208dbcdfa6c566b69c65cdaad49cadcf75524c3c1a770621403d", "blockNumber": "0x6882fc", "gasUsed": "0x253b2", "effectiveGasPrice": "0x19c85b", "from": "0x0000e6203db925dbfb9e3d650a80a1e2f4a78e94", "to": "0x82e8399ae0f58a6f4dbccd459e1fae6d80dbe852", "contractAddress": null}], "libraries": [], "pending": [], "returns": {}, "timestamp": 1728549186, "chain": 11155111, "commit": "3791740"}