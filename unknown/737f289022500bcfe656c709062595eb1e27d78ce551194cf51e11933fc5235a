# Global Pools

Malda introduces a new design paradigm, which are global pools on Ethereum Mainnet and its major rollups. In the Malda architecture a pool has multiple components, `Market_Contract_onHost, Market_Contract_onExtensions` connected by zero-knowledge proofs.

The protocol accounting is stored on Linea in the Host Contracts, but funds are also stored on the Extension contract of each market. Withdrawal type of actions (withdraw, borrow) cannot be executed without a verified zero-knowledge proof securing the extension of the market. Deposit type of actions cannot be recorded on the host chain without a valid zero-knowledge proof.

The advantage of this design is:

* Global supply, borrow, repay and withdraw on any of the supported chains
* Creating a single, global interest rate for assets based on the global utilization rate
* Removing fragmentation of supported assets across the different chains (LineaETH vs BaseETH)
* Deeper Liquidity and higher capital efficiency
