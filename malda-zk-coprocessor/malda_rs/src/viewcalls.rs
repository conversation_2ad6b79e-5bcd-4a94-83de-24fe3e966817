// Copyright (c) 2025 Merge Layers Inc.
//
// This source code is licensed under the Business Source License 1.1
// (the "License"); you may not use this file except in compliance with the
// License. You may obtain a copy of the License at
//
//     https://github.com/malda-protocol/malda-zk-coprocessor/blob/main/LICENSE-BSL
//
// See the License for the specific language governing permissions and
// limitations under the License.
//
//
//! Malda view call utilities for cross-chain proof of the get_proof_data view callon the Malda Protocol.
//!
//! This module provides comprehensive functionality for executing and proving user proof data queries
//! across multiple EVM chains with support for various L2 solutions and security mechanisms. The resulting proof can be submitted
//! to the Malda smart contracts for cross-chain lending actions.
//!
//! ## Core Features
//!
//! - **Multi-chain Proof Data Queries**: Execute and prove user proof data queries across multiple EVM chains
//! - **Sequencer Commitment Handling**: Process sequencer commitments for L2 chains (Optimism, Base)
//! - **L1 Block Verification**: Verify L1 block data for L2 chains with dispute game validation
//! - **Reorg Protection**: Manage linking blocks to protect against chain reorganizations
//! - **Bonsai Integration**: Generate ZK proofs using the Bonsai SDK
//! - **Boundless Market Integration**: Submit proof requests to the Boundless market for decentralized proving
//!
//! ## Supported Networks
//!
//! The module supports both mainnet and testnet (Sepolia) environments for:
//! - **Ethereum (L1)**
//! - **Optimism**:
//! - **Base**
//! - **Linea**
//!
//! ## Security Features
//!
//! - **Reorg Protection**: Configurable depth protection against chain reorganizations
//! - **Dispute Game Validation**: For OpStack chains, validates finalized dispute games
//! - **Proof Maturity Checks**: Ensures sufficient time has passed since dispute resolution
//! - **Blacklist Verification**: Checks that dispute games are not blacklisted
//! - **Game Type Validation**: Verifies dispute games use the correct game type


use crate::constants::*;
use crate::elfs_ids::*;
use crate::types::*;
use crate::types::{
    Call3, ExecutionPayload, IDisputeGame, IDisputeGameFactory, IL1Block, IL1MessageService,
    IMulticall3, SequencerCommitment,
};

use core::panic;

use risc0_op_steel::{
    optimism::{OpEvmEnv, OpEvmInput, OP_MAINNET_CHAIN_SPEC},
    DisputeGameIndex,
};
use risc0_steel::{
    ethereum::{EthEvmEnv, EthEvmFactory, ETH_MAINNET_CHAIN_SPEC},
    host::BlockNumberOrTag,
    serde::RlpHeader,
    Contract, EvmInput,
};
use risc0_zkvm::{
    default_executor, default_prover, ExecutorEnv, ProveInfo, ProverOpts, SessionInfo,
};

use alloy::primitives::{Address, Bytes, U256, U64};
use alloy_consensus::Header;

use anyhow::{Error, Result};
use bonsai_sdk;
use futures::future::join_all;
use tokio;
use url::Url;

use std::time::Duration;

use bonsai_sdk::blocking::Client;
use risc0_zkvm::Receipt;
use tracing::info;

use dotenvy;

use alloy::{signers::local::PrivateKeySigner, sol_types::SolValue};
use anyhow::{bail, Context};
use boundless_market::{
    storage::storage_provider_from_env, Client as BoundlessClient
};
use std::str::FromStr;

/// Timeout duration for transaction confirmation.
///
/// The maximum time to wait for a transaction to be confirmed on the blockchain.
/// This is used for operations that require transaction confirmation, such as
/// submitting proof requests to the Boundless market.
pub const TX_TIMEOUT: Duration = Duration::from_secs(30);

/// Statistics for a Malda ZK proof session.
///
/// This struct contains detailed information about the computational resources used during proof generation.
/// It is useful for auditing, performance analysis, and understanding the cost of ZK proof generation.
///
/// Fields:
/// - `segments`: Number of proof segments generated by the ZKVM session.
/// - `total_cycles`: Total computational cycles used (all segments).
/// - `user_cycles`: User-specific computational cycles (cycles spent on user logic).
/// - `paging_cycles`: Cycles used for memory paging operations (if any).
/// - `reserved_cycles`: Reserved cycles for system operations (if any).
#[derive(Debug, Clone)]
pub struct MaldaSessionStats {
    pub segments: usize,
    pub total_cycles: u64,
    pub user_cycles: u64,
    pub paging_cycles: u64,
    pub reserved_cycles: u64,
}

/// Information about a completed Malda ZK proof.
///
/// This struct contains the proof receipt, session statistics, and timing information for a ZK proof session.
/// It is returned by proof generation functions and is used for both on-chain and off-chain verification.
///
/// Fields:
/// - `receipt`: The ZK proof receipt containing the proof data (can be submitted on-chain).
/// - `stats`: Statistics about the proof generation session (see `MaldaSessionStats`).
/// - `uuid`: Unique identifier for the proof session (from Bonsai SDK).
/// - `stark_time`: Time taken for STARK proof generation in seconds.
/// - `snark_time`: Time taken for SNARK proof generation in seconds.
#[derive(Debug)]
pub struct MaldaProveInfo {
    pub receipt: Receipt,
    pub stats: MaldaSessionStats,
    pub uuid: String,
    pub stark_time: u64,
    pub snark_time: u64,
}

/// Runs a Bonsai ZK proof session with the provided input data.
///
/// This function handles the complete Bonsai SDK workflow:
/// 1. Uploads input data to Bonsai
/// 2. Creates a proof session
/// 3. Polls for session completion (STARK phase)
/// 4. Creates a SNARK session
/// 5. Polls for SNARK completion
/// 6. Downloads and deserializes the proof receipt
///
/// The function provides detailed timing information for both STARK and SNARK phases.
///
/// # Arguments
/// * `input_data` - The serialized input data for the ZKVM session.
///
/// # Returns
/// * `Result<MaldaProveInfo, anyhow::Error>` - Proof information and statistics if successful, or an error.
///
/// # Errors
/// Returns an error if:
/// - The Bonsai client fails to initialize.
/// - The input upload, session creation, or polling fails.
/// - The SNARK proof or receipt download fails.
/// - The receipt cannot be deserialized.
/// - Session status indicates failure.
///
/// # Panics
/// Panics if the required environment variable `IMAGE_ID_BONSAI` is not set.
///
/// # Environment Variables
/// Requires the following environment variable:
/// - `IMAGE_ID_BONSAI`: Bonsai image ID for ZK proof generation
fn run_bonsai(input_data: Vec<u8>) -> Result<MaldaProveInfo, anyhow::Error> {
    // Initialize the Bonsai client from environment variables (uses RISC Zero version for compatibility)
    let client = Client::from_env(risc0_zkvm::VERSION)?;

    // Get the Bonsai image ID from the environment (required for proof session)
    let image_id_hex: String =
        dotenvy::var("IMAGE_ID_BONSAI").expect("IMAGE_ID_BONSAI must be set in environment");

    // Upload the input data to Bonsai and get an input ID
    let input_id = client.upload_input(input_data)?;

    let assumptions: Vec<String> = vec![];
    let execute_only = false;

    // Create a new proof session on Bonsai
    let session = client.create_session(image_id_hex, input_id, assumptions, execute_only)?;

    let polling_interval = Duration::from_millis(500);

    // --- STARK phase: Wait for the session to complete and collect stats ---
    let stark_time = std::time::Instant::now();
    let succinct_stats = loop {
        let res = session.status(&client)?;
        if res.status == "RUNNING" {
            // Session is still running, wait and poll again
            std::thread::sleep(polling_interval);
            continue;
        }
        if res.status == "SUCCEEDED" {
            // Session succeeded, extract stats
            let stats = res
                .stats
                .expect("Missing stats object on Bonsai status res");
            tracing::debug!(
                "Bonsai usage: cycles: {} total_cycles: {}",
                stats.cycles,
                stats.total_cycles
            );

            break MaldaSessionStats {
                segments: stats.segments,
                total_cycles: stats.total_cycles,
                user_cycles: stats.cycles,
                paging_cycles: 0, // Paging cycles not tracked in this context
                reserved_cycles: 0, // Reserved cycles not tracked in this context
            };
        } else {
            // Session failed or exited unexpectedly
            return Err(anyhow::Error::msg(format!(
                "Bonsai prover workflow [{}] exited: {} err: {}",
                session.uuid,
                res.status,
                res.error_msg
                    .unwrap_or("Bonsai workflow missing error_msg".into())
            )));
        }
    };
    let stark_time = stark_time.elapsed();

    // --- SNARK phase: Create a SNARK session and wait for completion ---
    let snark_session = client.create_snark(session.uuid.clone())?;

    let start = std::time::Instant::now();
    let snark_receipt_url = loop {
        let res = snark_session.status(&client)?;
        match res.status.as_str() {
            "RUNNING" => {
                // SNARK session is still running, wait and poll again
                std::thread::sleep(polling_interval);
                continue;
            }
            "SUCCEEDED" => {
                // SNARK session succeeded, get the output URL
                break res.output.ok_or_else(|| {
                    anyhow::Error::msg(format!(
                        "Bonsai prover workflow [{}] reported success, but provided no receipt",
                        snark_session.uuid
                    ))
                })?;
            }
            _ => {
                // SNARK session failed or exited unexpectedly
                return Err(anyhow::Error::msg(format!(
                    "Bonsai prover workflow [{}] exited: {} err: {}",
                    snark_session.uuid,
                    res.status,
                    res.error_msg
                        .unwrap_or("Bonsai workflow missing error_msg".into())
                )));
            }
        }
    };

    let snark_time = start.elapsed();

    // Download the Groth16 receipt (proof) from Bonsai and deserialize it
    let receipt_buf = client.download(&snark_receipt_url)?;
    let groth16_receipt: Receipt = bincode::deserialize(&receipt_buf)?;

    Ok(MaldaProveInfo {
        receipt: groth16_receipt,
        stats: succinct_stats,
        uuid: session.uuid,
        stark_time: stark_time.as_secs(),
        snark_time: snark_time.as_secs(),
    })
}


/// Submits a proof data request to the Boundless market for decentralized proving.
///
/// This function creates a proof request and submits it to the Boundless market, which will
/// handle the ZK proof generation in a decentralized manner. The function waits for the
/// request to be fulfilled and returns the proof journal and seal.
///
/// The function supports both onchain and offchain submission modes:
/// - **Offchain**: Uses `client.submit_offchain()` for gasless submission
/// - **Onchain**: Uses `client.submit_onchain()` for on-chain submission with gas costs
///
/// # Arguments
/// * `users` - Vector of user address vectors, one per chain.
/// * `markets` - Vector of market contract address vectors, one per chain.
/// * `target_chain_id` - Vector of target chain IDs to query (vector of vectors).
/// * `chain_ids` - Vector of chain IDs to query.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs (default: false).
/// * `onchain` - Whether to submit onchain (true) or offchain (false).
///
/// # Returns
/// * `Result<(Bytes, Bytes), Error>` - Tuple of (journal, seal) if successful, or an error.
///
/// # Errors
/// Returns an error if:
/// - Environment variables `RPC_URL` or `PRIVATE_KEY` are not set.
/// - The Boundless client fails to initialize.
/// - The request submission or fulfillment fails.
/// - The input data preparation fails.
///
/// # Environment Variables
/// Requires the following environment variables:
/// - `RPC_URL`: Ethereum RPC endpoint for transactions
/// - `PRIVATE_KEY`: Private key for signing transactions
///
/// Optional environment variables:
/// - `PROGRAM_URL`: URL of a pre-uploaded program to avoid re-upload latency
pub async fn get_proof_data_prove_boundless(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_id: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
    fallback: bool,
    onchain: bool,
) -> Result<(Bytes, Bytes), Error> {
    // Only initialize tracing if it hasn't been set up already
    if tracing_subscriber::util::SubscriberInitExt::try_init(
        tracing_subscriber::fmt()
            .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
    ).is_err() {
        // Tracing is already initialized, which is fine
        tracing::debug!("Tracing subscriber already initialized");
    }

    // Load environment variables from .env if present
    match dotenvy::dotenv() {
        Ok(path) => tracing::debug!("Loaded environment variables from {:?}", path),
        Err(e) if e.not_found() => tracing::debug!("No .env file found"),
        Err(e) => bail!("failed to load .env file: {}", e),
    }

    // Get required environment variables for RPC and signing
    let rpc_url = dotenvy::var("RPC_URL").context("RPC_URL environment variable not set")?;
    let private_key =
        dotenvy::var("PRIVATE_KEY").context("PRIVATE_KEY environment variable not set")?;

    let rpc_url = Url::parse(&rpc_url)?;
    let private_key = PrivateKeySigner::from_str(&private_key)?;

    // Create a Boundless client from the provided parameters.
    let client = BoundlessClient::builder()
        .with_storage_provider(Some(storage_provider_from_env()?))
        .with_rpc_url(rpc_url)
        .with_private_key(private_key)
        .build()
        .await
        .context("failed to build boundless client")?;

    // Prepare the input bytes for the proof request (serializes all chain data)
    let input_bytes = get_proof_data_input(
        users,
        markets,
        target_chain_id,
        chain_ids,
        l1_inclusion,
        fallback,
    )
    .await;

    // Build the request - use program URL if available to avoid re-upload
    let request = if let Ok(program_url) = dotenvy::var("PROGRAM_URL") {
        tracing::info!("Using pre-uploaded program from URL: {}", program_url);
        let parsed_url = Url::parse(&program_url)
            .context("Failed to parse PROGRAM_URL")?;
        client
            .new_request()
            .with_program_url(parsed_url)?
            .with_stdin(input_bytes)
            .with_groth16_proof()
    } else {
        tracing::info!("No PROGRAM_URL found, uploading program directly");
        client
            .new_request()
            .with_program(GET_PROOF_DATA_ELF)
            .with_stdin(input_bytes)
            .with_groth16_proof()
    };

    // Submit the request to the Boundless market (onchain or offchain)
    let (request_id, expires_at) = if onchain {
        client.submit_onchain(request).await?
    } else {
        client.submit_offchain(request).await?
    };

    // Wait for the request to be fulfilled. The market will return the journal and seal.
    tracing::info!("Waiting for request {:x} to be fulfilled", request_id);
    let (journal, seal) = client
        .wait_for_request_fulfillment(
            request_id,
            Duration::from_secs(5), // check every 5 seconds
            expires_at,
        )
        .await?;
    tracing::info!("Request {:x} fulfilled", request_id);

    Ok((journal, seal))
}

/// Executes proof data queries across multiple chains in parallel.
///
/// This function executes ZKVM sessions for proof data queries without generating
/// full proofs. It's useful for testing and validation purposes. The function
/// processes each chain's proof data in parallel and then executes the ZKVM.
///
/// # Arguments
/// * `users` - Vector of user address vectors, one per chain.
/// * `markets` - Vector of market contract address vectors, one per chain.
/// * `target_chain_id` - Vector of target chain IDs to query (vector of vectors).
/// * `chain_ids` - Vector of chain IDs to query.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs (default: false).
///
/// # Returns
/// * `Result<SessionInfo, Error>` - Session info from the ZKVM execution.
///
/// # Errors
/// Returns an error if:
/// - Array lengths don't match.
/// - RPC calls fail.
/// - ZKVM execution fails.
/// - Parallel task execution fails.
pub async fn get_proof_data_exec(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_id: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
    fallback: bool,
) -> Result<SessionInfo, Error> {
    // Ensure all input vectors are the same length for parallel processing
    assert_eq!(
        users.len(),
        markets.len(),
        "Users and markets array lengths must match"
    );
    assert_eq!(
        users.len(),
        chain_ids.len(),
        "Users and chain_ids array lengths must match"
    );

    // Spawn a parallel async task for each chain's proof data input
    let futures: Vec<_> = (0..chain_ids.len())
        .map(|i| {
            let users = users[i].clone();
            let markets = markets[i].clone();
            let target_chain_id = target_chain_id[i].clone();
            let chain_id = chain_ids[i];
            let fallback = fallback;
            tokio::spawn(async move {
                get_proof_data_zkvm_input(
                    users,
                    markets,
                    target_chain_id,
                    chain_id,
                    l1_inclusion,
                    fallback,
                )
                .await
            })
        })
        .collect();

    // Wait for all tasks to complete and collect their results
    let results = join_all(futures).await;
    let all_inputs = results
        .into_iter()
        .map(|r| r.expect("Failed to join parallel execution task"))
        .flatten()
        .collect::<Vec<u8>>();

    // Build the ZKVM executor environment with all chain inputs
    let env = ExecutorEnv::builder()
        .write(&(chain_ids.len() as u64))
        .expect("Failed to write chain count to executor environment")
        .write_slice(&all_inputs)
        .build()
        .expect("Failed to build executor environment");

    // Execute the ZKVM with the prepared environment and return the session info
    Ok(default_executor()
        .execute(env, GET_PROOF_DATA_ELF)
        .expect("Failed to execute ZKVM"))
}

/// Creates the executor environment with proof data from multiple chains.
///
/// This function prepares the ZKVM executor environment by collecting proof data inputs
/// from multiple chains in parallel and combining them into a single environment.
///
/// The function processes each chain's proof data independently and then combines
/// all inputs into a single executor environment for ZKVM execution.
///
/// # Arguments
/// * `users` - Vector of user address vectors, one per chain.
/// * `markets` - Vector of market contract address vectors, one per chain.
/// * `target_chain_ids` - Vector of target chain IDs to query (vector of vectors).
/// * `chain_ids` - Vector of chain IDs to query.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `ExecutorEnv<'static>` - Environment configured with proof data inputs.
///
/// # Panics
/// Panics if:
/// - Array lengths don't match.
/// - Parallel task execution fails.
/// - Environment building fails.
async fn get_proof_data_env(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
    fallback: bool,
) -> ExecutorEnv<'static> {
    // Ensure all input vectors are the same length for parallel processing
    assert_eq!(users.len(), markets.len());
    assert_eq!(users.len(), chain_ids.len());

    // Spawn a parallel async task for each chain's proof data input
    let futures: Vec<_> = (0..chain_ids.len())
        .map(|i| {
            let users = users[i].clone();
            let markets = markets[i].clone();
            let chain_id = chain_ids[i];
            let target_chain_id = target_chain_ids[i].clone();
            let fallback = fallback;
            tokio::spawn(async move {
                get_proof_data_zkvm_input(
                    users,
                    markets,
                    target_chain_id,
                    chain_id,
                    l1_inclusion,
                    fallback,
                )
                .await
            })
        })
        .collect();

    // Wait for all tasks to complete and collect their results
    let results = join_all(futures).await;
    let all_inputs = results
        .into_iter()
        .filter_map(|r| r.ok())
        .flat_map(|input| input)
        .collect::<Vec<_>>();

    // Build the ZKVM executor environment with all chain inputs
    ExecutorEnv::builder()
        .write(&(chain_ids.len() as u64))
        .unwrap()
        .write_slice(&all_inputs)
        .build()
        .unwrap()
}

/// Prepares input data for the ZKVM for multiple chains' proof data queries.
///
/// This function prepares the input data for Bonsai SDK proof generation by collecting
/// proof data inputs from multiple chains in parallel and serializing them into the
/// format expected by the Bonsai SDK.
///
/// The function processes each chain's proof data independently and then combines
/// all inputs into a single serialized format for Bonsai processing.
///
/// # Arguments
/// * `users` - Vector of user address vectors, one per chain.
/// * `markets` - Vector of market contract address vectors, one per chain.
/// * `target_chain_ids` - Vector of target chain IDs to query (vector of vectors).
/// * `chain_ids` - Vector of chain IDs to query.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `Vec<u8>` - Serialized input data for the ZKVM.
///
/// # Panics
/// Panics if:
/// - Array lengths don't match.
/// - Parallel task execution fails.
/// - Serialization fails.
async fn get_proof_data_input(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
    fallback: bool,
) -> Vec<u8> {
    // Ensure all input vectors are the same length for parallel processing
    assert_eq!(users.len(), markets.len());
    assert_eq!(users.len(), chain_ids.len());

    // Spawn a parallel async task for each chain's proof data input
    let futures: Vec<_> = (0..chain_ids.len())
        .map(|i| {
            let users = users[i].clone();
            let markets = markets[i].clone();
            let chain_id = chain_ids[i];
            let target_chain_id = target_chain_ids[i].clone();
            let fallback = fallback;
            tokio::spawn(async move {
                get_proof_data_zkvm_input(
                    users,
                    markets,
                    target_chain_id,
                    chain_id,
                    l1_inclusion,
                    fallback,
                )
                .await
            })
        })
        .collect();

    // Wait for all tasks to complete and collect their results
    let results = join_all(futures).await;
    let all_inputs = results
        .into_iter()
        .filter_map(|r| r.ok())
        .flat_map(|input| input)
        .collect::<Vec<_>>();

    // Serialize the number of chains as the first field (required by the ZKVM input format)
    let input: Vec<u8> = bytemuck::pod_collect_to_vec(
        &risc0_zkvm::serde::to_vec(&(chain_ids.len() as u64)).unwrap(),
    );

    // Concatenate the chain count and all chain inputs into a single input vector
    [input, all_inputs].concat()
}

/// Generates ZK proofs for proof data queries across multiple chains.
///
/// This function generates ZK proofs using the local RISC Zero prover. It runs the
/// proof generation in a blocking task to avoid blocking the async runtime. The function
/// provides detailed timing information for environment creation and proof generation.
///
/// # Arguments
/// * `users` - Vector of user address vectors, one per chain.
/// * `markets` - Vector of market contract address vectors, one per chain.
/// * `target_chain_ids` - Vector of target chain IDs to query (vector of vectors).
/// * `chain_ids` - Vector of chain IDs to query.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs (default: false).
///
/// # Returns
/// * `Result<ProveInfo, Error>` - Proof information from the ZKVM.
///
/// # Errors
/// Returns an error if:
/// - Array lengths don't match.
/// - RPC calls fail.
/// - Proof generation fails.
/// - ZKVM execution fails.
pub async fn get_proof_data_prove(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
    fallback: bool,
) -> Result<ProveInfo, Error> {
    // Run the proof generation in a blocking task to avoid blocking the async runtime
    let prove_info = tokio::task::spawn_blocking(move || {
        let rt = tokio::runtime::Runtime::new().unwrap();

        // Time the environment creation step
        let start_time = std::time::Instant::now();
        let env = rt.block_on(get_proof_data_env(
            users,
            markets,
            target_chain_ids,
            chain_ids,
            l1_inclusion,
            fallback,
        ));
        let duration = start_time.elapsed();
        info!("Env creation time: {:?}", duration);

        // Time the proof generation step
        let start_time = std::time::Instant::now();
        let proof =
            default_prover().prove_with_opts(env, GET_PROOF_DATA_ELF, &ProverOpts::groth16());
        let duration = start_time.elapsed();
        info!("Bonsai proof time: {:?}", duration);
        proof
    })
    .await?;

    prove_info
}

/// Generates ZK proofs for proof data queries across multiple chains using the Bonsai SDK.
///
/// This function uses the Bonsai SDK to generate ZK proofs for proof data queries.
/// It runs the proof generation in a blocking task to avoid blocking the async runtime.
/// The function provides detailed timing information for both STARK and SNARK phases.
///
/// # Arguments
/// * `users` - Vector of user address vectors, one per chain.
/// * `markets` - Vector of market contract address vectors, one per chain.
/// * `target_chain_ids` - Vector of target chain IDs to query (vector of vectors).
/// * `chain_ids` - Vector of chain IDs to query.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs (default: false).
///
/// # Returns
/// * `Result<MaldaProveInfo, Error>` - Proof information from the Bonsai SDK.
///
/// # Errors
/// Returns an error if:
/// - Array lengths don't match.
/// - RPC calls fail.
/// - Proof generation fails.
/// - Bonsai SDK operations fail.
pub async fn get_proof_data_prove_sdk(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
    fallback: bool,
) -> Result<MaldaProveInfo, Error> {
    // Run the proof generation in a blocking task to avoid blocking the async runtime
    let prove_info = tokio::task::spawn_blocking(move || {
        let rt = tokio::runtime::Runtime::new().unwrap();

        // Time the environment creation step
        let start_time = std::time::Instant::now();
        let input = rt.block_on(get_proof_data_input(
            users,
            markets,
            target_chain_ids,
            chain_ids,
            l1_inclusion,
            fallback,
        ));
        let duration = start_time.elapsed();
        info!("Env creation time: {:?}", duration);

        // Time the Bonsai proof generation step
        let start_time = std::time::Instant::now();
        let proof = run_bonsai(input);
        let duration = start_time.elapsed();
        info!("Bonsai proof time: {:?}", duration);
        proof
    })
    .await?;

    prove_info
}


/// Prepares input data for the ZKVM for a single chain's proof data queries.
///
/// This function orchestrates the entire proof data preparation process for a single chain:
/// 1. Gets sequencer commitments and block numbers
/// 2. Prepares L1 block call inputs if needed
/// 3. Handles L1 inclusion environment setup
/// 4. Fetches linking blocks for reorg protection
/// 5. Prepares proof data call inputs
/// 6. Serializes everything into ZKVM input format
///
/// The function handles different chain types (OpStack, Linea, Ethereum) with their
/// specific requirements for L1 inclusion and block validation.
///
/// # Arguments
/// * `users` - Vector of user addresses to query.
/// * `markets` - Vector of market contract addresses to query.
/// * `target_chain_ids` - Vector of target chain IDs to query.
/// * `chain_id` - Chain ID for the queries.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `Vec<u8>` - Serialized input data for the ZKVM.
///
/// # Panics
/// Panics if:
/// - Invalid chain ID is provided.
/// - RPC calls fail.
/// - Required block numbers are not available.
/// - Serialization fails.
pub async fn get_proof_data_zkvm_input(
    users: Vec<Address>,
    markets: Vec<Address>,
    target_chain_ids: Vec<u64>,
    chain_id: u64,
    l1_inclusion: bool,
    fallback: bool,
) -> Vec<u8> {
    // Determine if the chain is a Sepolia testnet variant
    let is_sepolia = matches!(
        chain_id,
        OPTIMISM_SEPOLIA_CHAIN_ID
            | BASE_SEPOLIA_CHAIN_ID
            | ETHEREUM_SEPOLIA_CHAIN_ID
            | LINEA_SEPOLIA_CHAIN_ID
    );

    // Get the chain name and testnet status for RPC URL selection
    let (chain_name, is_testnet) = get_chain_params(chain_id);
    let rpc_url = get_rpc_url(chain_name, fallback, is_testnet);

    // Fetch sequencer commitments and block numbers for the chain
    let (block, commitment, block_2, commitment_2) =
        get_sequencer_commitments_and_blocks(chain_id, rpc_url, is_sepolia, l1_inclusion, fallback)
            .await;

    // Prepare L1 block call inputs and block numbers if needed
    let (l1_block_call_input_1, ethereum_block_1, l1_block_call_input_2, _ethereum_block_2) =
        get_l1block_call_inputs_and_l1_block_numbers(
            chain_id,
            is_sepolia,
            l1_inclusion,
            block,
            block_2,
            fallback,
        )
        .await;

    // Prepare environment input for L1 inclusion and L2 block number
    let (env_input_l1_inclusion, l2_block_number_on_l1) =
        get_env_input_for_l1_inclusion_and_l2_block_number(
            chain_id,
            is_sepolia,
            l1_inclusion,
            ethereum_block_1,
            fallback,
        )
        .await;

    // Determine the block number to use for linking blocks and proof data call input
    let block = if l1_inclusion && is_linea_chain(chain_id) {
        l2_block_number_on_l1.unwrap()
    } else if is_ethereum_chain(chain_id) || (is_opstack_chain(chain_id) && l1_inclusion) {
        ethereum_block_1.unwrap()
    } else {
        block.unwrap()
    };

    // Determine which chain and RPC URL to use for reorg protection linking blocks
    let (chaind_id_linking_blocks, rpc_url_linking_blocks) =
        if is_opstack_chain(chain_id) && l1_inclusion {
            let (ethereum_chain_id, is_ethereum_testnet) =
                if matches!(chain_id, OPTIMISM_CHAIN_ID | BASE_CHAIN_ID) {
                    (ETHEREUM_CHAIN_ID, false)
                } else {
                    (ETHEREUM_SEPOLIA_CHAIN_ID, true)
                };
            (
                ethereum_chain_id,
                get_rpc_url("ETHEREUM", fallback, is_ethereum_testnet),
            )
        } else {
            (chain_id, rpc_url)
        };

    // Fetch linking blocks for reorg protection and prepare proof data call input in parallel
    let (linking_blocks, (proof_data_call_input, proof_data_call_input_op)) = tokio::join!(
        get_linking_blocks(chaind_id_linking_blocks, rpc_url_linking_blocks, block),
        get_proof_data_call_input(
            chain_id,
            rpc_url,
            block,
            users.clone(),
            markets.clone(),
            target_chain_ids.clone(),
            l1_inclusion,
            fallback,
        )
    );

    // Serialize all inputs into the format expected by the ZKVM guest
    let input: Vec<u8> = bytemuck::pod_collect_to_vec(
        &risc0_zkvm::serde::to_vec(&(
            &proof_data_call_input,
            &chain_id,
            &users,
            &markets,
            &target_chain_ids,
            &commitment,
            &l1_block_call_input_1,
            &linking_blocks,
            &env_input_l1_inclusion,
            &proof_data_call_input_op,
            &commitment_2,
            &l1_block_call_input_2,
        ))
        .unwrap(),
    );

    input
}

/// Returns the environment input for L1 inclusion and the L2 block number for a given chain.
/// This function handles non-OpStack chains (Ethereum and Linea).
///
/// For OpStack chains, this function delegates to `get_env_input_for_opstack_dispute_game`.
/// For Linea chains, it calls `get_env_input_for_linea_l1_call` to get L2 block information.
///
/// # Arguments
/// * `chain_id` - The chain ID to query.
/// * `is_sepolia` - Whether the chain is a Sepolia testnet variant.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `ethereum_block` - The Ethereum block number (optional, required if l1_inclusion is true).
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(Option<EvmInput<EthEvmFactory>>, Option<u64>)` - The environment input and L2 block number, if available.
///
/// # Panics
/// Panics if:
/// - L1 inclusion is requested for an unsupported chain.
/// - OpStack chain ID is provided (use get_env_input_for_opstack_l1_inclusion instead).
/// - Ethereum block number is not provided when l1_inclusion is true.
pub async fn get_env_input_for_l1_inclusion_and_l2_block_number(
    chain_id: u64,
    is_sepolia: bool,
    l1_inclusion: bool,
    ethereum_block: Option<u64>,
    fallback: bool,
) -> (Option<EvmInput<EthEvmFactory>>, Option<u64>) {
    if !l1_inclusion {
        // If L1 inclusion is not required, return None for both values
        (None, None)
    } else {
        // Prepare the L1 RPC URL
        let l1_rpc_url = get_rpc_url("ETHEREUM", fallback, is_sepolia);
        // Determine the L1 block to use for inclusion
        let l1_block = if is_linea_chain(chain_id) {
            ethereum_block.unwrap()
        } else {
            if is_sepolia {
                ethereum_block.unwrap() - REORG_PROTECTION_DEPTH_ETHEREUM_SEPOLIA
            } else if !is_sepolia {
                ethereum_block.unwrap() - REORG_PROTECTION_DEPTH_ETHEREUM
            } else {
                panic!("Invalid chain ID");
            }
        };

        // Delegate to the appropriate helper based on chain type
        if is_opstack_chain(chain_id) {
            get_env_input_for_opstack_dispute_game(chain_id, l1_block, fallback).await
        } else if is_linea_chain(chain_id) {
            get_env_input_for_linea_l1_call(chain_id, l1_rpc_url, l1_block).await
        } else {
            panic!(
                "L1 Inclusion only supported for Optimism, Base, Linea and their Sepolia variants"
            );
        }
    }
}

/// Returns the environment input for L1 inclusion and the L2 block number for OpStack chains.
///
/// This is a wrapper function that delegates to `get_env_input_for_opstack_dispute_game`
/// for OpStack chains. It provides a consistent interface for L1 inclusion handling.
///
/// # Arguments
/// * `chain_id` - The chain ID to query (must be an OpStack chain).
/// * `l1_block` - The L1 block number.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(Option<EvmInput<EthEvmFactory>>, Option<u64>)` - The environment input and L2 block number, if available.
///
/// # Panics
/// Panics if:
/// - Non-OpStack chain ID is provided.
/// - Dispute game validation fails.
pub async fn get_env_input_for_opstack_l1_inclusion(
    chain_id: u64,
    l1_block: u64,
    fallback: bool,
) -> (Option<EvmInput<EthEvmFactory>>, Option<u64>) {
    if !is_opstack_chain(chain_id) {
        panic!("This function only supports OpStack chains");
    }
    get_env_input_for_opstack_dispute_game(chain_id, l1_block, fallback).await
}

/// Returns the environment input for OpStack dispute game and a dummy L2 block number.
///
/// This function performs comprehensive validation of the dispute game state:
/// 1. Builds OpStack environment with dispute game from RPC
/// 2. Validates game type is correct (0 = fault game)
/// 3. Checks game was created after respected game type update
/// 4. Verifies game status is DEFENDER_WINS
/// 5. Ensures game is not blacklisted
/// 6. Validates sufficient time has passed since game resolution
/// 7. Confirms root claim matches the commitment
///
/// # Arguments
/// * `chain_id` - The chain ID to query (must be an OpStack chain).
/// * `l1_block` - The L1 block number for the dispute game.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(Option<EvmInput<EthEvmFactory>>, Option<u64>)` - The environment input and a dummy L2 block number.
///
/// # Panics
/// Panics if:
/// - Invalid chain ID is provided.
/// - Dispute game validation fails (wrong game type, status, blacklisted, etc.).
/// - Insufficient time has passed since game resolution.
/// - Root claim does not match the commitment.
pub async fn get_env_input_for_opstack_dispute_game(
    chain_id: u64,
    l1_block: u64,
    fallback: bool,
) -> (Option<EvmInput<EthEvmFactory>>, Option<u64>) {
    // Get OpStack configuration (RPC URLs, portal address, etc.)
    let (l1_rpc_url, optimism_portal, l2_rpc_url, _chain_name) =
        get_opstack_config(chain_id, fallback);

    // Build the Ethereum environment for the L1 block
    let mut env = EthEvmEnv::builder()
        .rpc(Url::parse(l1_rpc_url).expect("Failed to parse RPC URL"))
        .block_number_or_tag(BlockNumberOrTag::Number(l1_block))
        .chain_spec(&ETH_MAINNET_CHAIN_SPEC)
        .build()
        .await
        .expect("Failed to build EVM environment");
    // Build the OpStack environment with the dispute game
    let builder = OpEvmEnv::builder()
        .dispute_game_from_rpc(
            optimism_portal,
            Url::parse(l1_rpc_url).expect("Failed to parse RPC URL"),
        )
        .game_index(DisputeGameIndex::Finalized);
    let mut op_env = builder
        .rpc(Url::parse(l2_rpc_url).expect("Failed to parse RPC URL"))
        .chain_spec(&OP_MAINNET_CHAIN_SPEC)
        .build()
        .await
        .expect("Failed to build OP-EVM environment");

    // This is just an arbitrary simple call needed in order to do into_env to get the game_index
    let mut contract = Contract::preflight(L1_BLOCK_ADDRESS_OPSTACK, &mut op_env);
    let block_hash_call = IL1Block::hashCall {};
    let _returns = contract
        .call_builder(&block_hash_call)
        .call()
        .await
        .expect("Failed to execute factory call");

    let input = op_env
        .into_input()
        .await
        .expect("Failed to convert environment to input");
    let op_env_commitment = input
        .clone()
        .into_env(&OP_MAINNET_CHAIN_SPEC)
        .into_commitment();

    let (game_index, _version) = op_env_commitment.decode_id();

    let root_claim = op_env_commitment.digest;

    let portal_adress = get_portal_address(chain_id);

    // Get the portal contract for additional checks
    let mut contract = Contract::preflight(portal_adress, &mut env);

    // Get factory address from portal
    let factory_call = IOptimismPortal::disputeGameFactoryCall {};
    let factory_address = contract
        .call_builder(&factory_call)
        .call()
        .await
        .expect("Failed to execute factory call");

    let game_call = IDisputeGameFactory::gameAtIndexCall { index: game_index };

    let mut contract = Contract::preflight(factory_address, &mut env);
    let returns = contract
        .call_builder(&game_call)
        .call()
        .await
        .expect("Failed to execute game at index call");

    let game_type = returns._0;
    assert_eq!(game_type, U256::from(0), "game type not respected game");

    let created_at = returns._1;
    let game_address = returns._2;

    // Check if game was created after respected game type update
    let mut contract = Contract::preflight(portal_adress, &mut env);
    let respected_game_type_updated_at_call = IOptimismPortal::respectedGameTypeUpdatedAtCall {};
    let updated_at = contract
        .call_builder(&respected_game_type_updated_at_call)
        .call()
        .await
        .expect("Failed to execute respected game type updated at call");
    assert!(
        created_at >= updated_at,
        "game created before respected game type update"
    );

    // Get game contract for status checks
    let mut contract = Contract::preflight(game_address, &mut env);

    // Check game status
    let status_call = IDisputeGame::statusCall {};
    let status = contract
        .call_builder(&status_call)
        .call()
        .await
        .expect("Failed to execute status call");
    assert_eq!(
        status,
        GameStatus::DEFENDER_WINS,
        "game status not DEFENDER_WINS"
    );

    // Check if game is blacklisted
    let mut contract = Contract::preflight(portal_adress, &mut env);
    let blacklist_call = IOptimismPortal::disputeGameBlacklistCall { game: game_address };
    let is_blacklisted = contract
        .call_builder(&blacklist_call)
        .call()
        .await
        .expect("Failed to execute blacklist call");
    assert!(!is_blacklisted, "game is blacklisted");

    // Check game resolution time
    let mut contract = Contract::preflight(game_address, &mut env);
    let resolved_at_call = IDisputeGame::resolvedAtCall {};
    let resolved_at = contract
        .call_builder(&resolved_at_call)
        .call()
        .await
        .expect("Failed to execute resolved at call");

    let mut contract = Contract::preflight(portal_adress, &mut env);
    let proof_maturity_delay_call = IOptimismPortal::proofMaturityDelaySecondsCall {};
    let proof_maturity_delay = contract
        .call_builder(&proof_maturity_delay_call)
        .call()
        .await
        .expect("Failed to execute proof maturity delay call");

    let current_timestamp = env.header().inner().inner().timestamp;
    assert!(
        U256::from(current_timestamp) - U256::from(resolved_at)
            > proof_maturity_delay - U256::from(300),
        "insufficient time passed since game resolution"
    );

    // Finally verify root claim matches
    let mut contract = Contract::preflight(game_address, &mut env);
    let root_claim_call = IDisputeGame::rootClaimCall {};
    let root = contract
        .call_builder(&root_claim_call)
        .call()
        .await
        .expect("Failed to execute root claim call");

    assert_eq!(root, root_claim, "root claim not respected");

    (
        Some(
            env.into_input()
                .await
                .expect("Failed to convert environment to input"),
        ),
        // irrelevant for l1 inclusion on opstack
        Some(1),
    )
}

/// Returns L1 block call inputs and L1 block numbers for a given chain.
///
/// This function prepares L1 block call inputs for chains that require L1 inclusion
/// or are Ethereum chains themselves. It currently only processes one block (block_2
/// is unused but kept for interface consistency).
///
/// This function is designed to support confirming the L1 block via both OP and Base sequencer commitments for additional security. However, the second path (Base) is currently disabled for latency reasons. Only the OP path is active.
///
/// # Arguments
/// * `chain_id` - The chain ID to query.
/// * `is_sepolia` - Whether the chain is a Sepolia testnet variant.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `block` - The block number (optional, required if l1_inclusion is true or chain is Ethereum).
/// * `_block_2` - The second block number (optional, unused, kept for interface consistency).
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(Option<EvmInput<EthEvmFactory>>, Option<u64>, Option<EvmInput<EthEvmFactory>>, Option<u64>)` -
///   Tuple of optional L1 block call inputs and block numbers.
///
/// # Panics
/// Panics if:
/// - Block number is not provided when required.
/// - L1 block call input generation fails.
pub async fn get_l1block_call_inputs_and_l1_block_numbers(
    chain_id: u64,
    is_sepolia: bool,
    l1_inclusion: bool,
    block: Option<u64>,
    _block_2: Option<u64>,
    fallback: bool,
) -> (
    Option<EvmInput<EthEvmFactory>>,
    Option<u64>,
    Option<EvmInput<EthEvmFactory>>,
    Option<u64>,
) {
    if is_ethereum_chain(chain_id) || l1_inclusion {
        // For Ethereum or L1 inclusion, prepare the L1 block call input for the appropriate chain
        let (chain_id_1, _chain_id_2) = match is_sepolia {
            true => (OPTIMISM_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID),
            false => (OPTIMISM_CHAIN_ID, BASE_CHAIN_ID),
        };
        let (l1_block_call_input_1, ethereum_block_1) = get_l1block_call_input(
            BlockNumberOrTag::Number(block.unwrap()),
            chain_id_1,
            fallback,
        )
        .await;
        // NOTE: The following code is intended to enable L1 block confirmation via both OP and Base for extra security, but is currently disabled for latency reasons. Only the OP path is active.
        // let (l1_block_call_input_2, ethereum_block_2) =
        //     get_l1block_call_input(BlockNumberOrTag::Number(block_2.unwrap()), chain_id_2, fallback).await;

        (
            Some(l1_block_call_input_1),
            Some(ethereum_block_1),
            None::<EvmInput<EthEvmFactory>>,
            None::<u64>,
        )
        // (Some(l1_block_call_input_1), Some(ethereum_block_1), Some(l1_block_call_input_2), Some(ethereum_block_2))
    } else {
        // For other chains, no L1 block call input is needed
        (None, None, None, None)
    }
}

/// Prepares multicall input for batch proof data checking.
///
/// This function creates a multicall to batch multiple `getProofData(address,uint32)` calls
/// for efficient proof data retrieval. It handles both standard EVM chains and OpStack chains
/// with L1 inclusion validation.
///
/// The function applies reorg protection by querying blocks that are sufficiently confirmed
/// based on the chain's protection depth.
///
/// # Arguments
/// * `chain_id` - Chain ID for the queries.
/// * `chain_url` - RPC URL for the chain.
/// * `block` - Block number to query at (will be adjusted for reorg protection).
/// * `users` - Vector of user addresses to query proof data for.
/// * `markets` - Vector of market contract addresses to query.
/// * `target_chain_ids` - Vector of target chain IDs to query proof data for.
/// * `validate_l1_inclusion` - Whether to validate L1 inclusion for OpStack chains.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(Option<EvmInput<EthEvmFactory>>, Option<OpEvmInput>)` - Formatted EVM input for the multicall and optional OpEvmInput.
///
/// # Panics
/// Panics if:
/// - Invalid chain ID is provided.
/// - RPC connection fails.
/// - Environment building fails.
pub async fn get_proof_data_call_input(
    chain_id: u64,
    chain_url: &str,
    block: u64,
    users: Vec<Address>,
    markets: Vec<Address>,
    target_chain_ids: Vec<u64>,
    validate_l1_inclusion: bool,
    fallback: bool,
) -> (Option<EvmInput<EthEvmFactory>>, Option<OpEvmInput>) {
    // Calculate the block number to use for reorg protection
    let reorg_protection_depth = get_reorg_protection_depth(chain_id);
    let block_reorg_protected = block - reorg_protection_depth;

    // Create array of Call3 structs for each proof data check
    let mut calls = Vec::with_capacity(users.len());

    for ((user, market), target_chain_id) in users
        .iter()
        .zip(markets.iter())
        .zip(target_chain_ids.iter())
    {
        // Selector for getProofData(address,uint32)
        let selector = [0x07, 0xd9, 0x23, 0xe9];
        let user_bytes: [u8; 32] = user.into_word().into();
        // Convert chain_id to 4 bytes
        let chain_id_bytes = (*target_chain_id as u32).to_be_bytes();

        // Create calldata by concatenating selector, encoded address, and chain ID
        let mut call_data = Vec::with_capacity(68); // 4 bytes selector + 32 bytes address + 4 bytes chain ID
        call_data.extend_from_slice(&selector);
        call_data.extend_from_slice(&user_bytes);
        call_data.extend_from_slice(&[0u8; 28]); // pad chain id to 32 bytes
        call_data.extend_from_slice(&chain_id_bytes);

        calls.push(Call3 {
            target: *market,
            allowFailure: false,
            callData: call_data.into(),
        });
    }

    // Make single multicall
    let multicall = IMulticall3::aggregate3Call { calls };

    // Use separate code paths for each environment type
    if is_opstack_chain(chain_id) && validate_l1_inclusion {
        // Build an environment based on the state of the latest finalized fault dispute game
        let (l1_rpc_url, optimism_portal, chain_url_final, _chain_name) =
            get_opstack_config(chain_id, !fallback);
        let mut env = OpEvmEnv::builder()
            .dispute_game_from_rpc(
                optimism_portal,
                Url::parse(l1_rpc_url).expect("Failed to parse RPC URL"),
            )
            .game_index(DisputeGameIndex::Finalized)
            .rpc(Url::parse(chain_url_final).map_err(|e| {
                eprintln!("ERROR parsing RPC URL in get_proof_data_call_input (op_env): {:?} - URL: {}", e, chain_url_final);
                e
            }).expect("Failed to parse RPC URL"))
            .chain_spec(&OP_MAINNET_CHAIN_SPEC)
            .build()
            .await
            .expect("Failed to build OP-EVM environment");

        let mut contract = Contract::preflight(MULTICALL, &mut env);
        let _returns = contract
            .call_builder(&multicall)
            // .gas_price(U256::from(gas_price))
            // .from(Address::ZERO)
            .call()
            .await
            .expect("Failed to execute multicall");

        (
            None,
            Some(
                env.into_input()
                    .await
                    .expect("Failed to convert environment to input"),
            ),
        )
    } else {
        let chain_url_final = if fallback {
            let (chain_name, is_testnet) = get_chain_params(chain_id);
            get_rpc_url(chain_name, true, is_testnet)
        } else {
            chain_url
        };
        let mut env = EthEvmEnv::builder()
        .rpc(Url::parse(chain_url_final).map_err(|e| {
            eprintln!("ERROR parsing RPC URL in get_proof_data_call_input (op_env): {:?} - URL: {}", e, chain_url_final);
            e
        }).expect("Failed to parse RPC URL"))
            .block_number_or_tag(BlockNumberOrTag::Number(block_reorg_protected))
            .chain_spec(&LINEA_MAINNET_CHAIN_SPEC)
            .build()
            .await
            .expect("Failed to build EVM environment");

        let mut contract = Contract::preflight(MULTICALL, &mut env);
        let _returns = contract
            .call_builder(&multicall)
            // .gas_price(U256::from(gas_price))
            // .from(Address::ZERO)
            .call()
            .await
            .expect("Failed to execute multicall");

        (
            Some(
                env.into_input()
                    .await
                    .expect("Failed to convert environment to input"),
            ),
            None,
        )
    }
}

/// Fetches sequencer commitments and block numbers for a given chain, handling L1 inclusion and Sepolia/mainnet variants.
///
/// This function handles different chain types:
/// - **OpStack chains**: Fetches sequencer commitments from the sequencer API
/// - **Ethereum chains**: Uses default sequencer chain for commitments
/// - **Linea chains**: Gets current block number directly from RPC
///
/// For L1 inclusion scenarios, it uses the default sequencer chain (Optimism) to get commitments.
///
/// # Arguments
/// * `chain_id` - The chain ID to query.
/// * `rpc_url` - The RPC URL for the chain.
/// * `is_sepolia` - Whether the chain is a Sepolia testnet variant.
/// * `l1_inclusion` - Whether to include L1 data in the proof.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(Option<u64>, Option<SequencerCommitment>, Option<u64>, Option<SequencerCommitment>)` -
///   Tuple of (block, commitment, block_2, commitment_2), where the second pair is only relevant for some Sepolia/mainnet cases.
///
/// # Panics
/// Panics if:
/// - An invalid chain ID is provided.
/// - RPC calls fail.
/// - Sequencer API requests fail.
pub async fn get_sequencer_commitments_and_blocks(
    chain_id: u64,
    rpc_url: &str,
    is_sepolia: bool,
    l1_inclusion: bool,
    fallback: bool,
) -> (
    Option<u64>,
    Option<SequencerCommitment>,
    Option<u64>,
    Option<SequencerCommitment>,
) {
    if is_opstack_chain(chain_id)
        || is_ethereum_chain(chain_id)
        || (is_linea_chain(chain_id) && l1_inclusion)
    {
        if !l1_inclusion && is_opstack_chain(chain_id) {
            // For OpStack chains without L1 inclusion, get the current sequencer commitment
            let (commitment, block) = get_current_sequencer_commitment(chain_id, fallback).await;
            (
                Some(block),
                Some(commitment),
                None::<u64>,
                None::<SequencerCommitment>,
            )
        } else {
            // For L1 inclusion or Ethereum chains, use the default sequencer chain
            let default_chain = get_default_sequencer_chain(chain_id, is_sepolia);
            let (commitment, block) =
                get_current_sequencer_commitment(default_chain, fallback).await;
            (Some(block), Some(commitment), None, None)
        }
    } else if is_linea_chain(chain_id) {
        // For Linea chains, get the current block number directly from RPC
        let block = EthEvmEnv::builder()
            .rpc(Url::parse(rpc_url).unwrap())
            .block_number_or_tag(BlockNumberOrTag::Latest)
            .chain_spec(&ETH_MAINNET_CHAIN_SPEC)
            .build()
            .await
            .unwrap()
            .header()
            .inner()
            .inner()
            .number;
        (Some(block), None, None, None)
    } else {
        panic!("Invalid chain ID");
    }
}
/// Fetches the current sequencer commitment for L2 chains.
///
/// This function queries the sequencer API to get the latest sequencer commitment
/// for OpStack chains (Optimism, Base). The commitment contains execution payload
/// data that can be used to verify L2 state.
///
/// # Arguments
/// * `chain_id` - Chain ID (Optimism, Base, or their Sepolia variants).
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(SequencerCommitment, u64)` - Tuple of sequencer commitment and block number.
///
/// # Panics
/// Panics if:
/// - Invalid chain ID is provided.
/// - Sequencer API request fails.
/// - JSON parsing fails.
/// - Execution payload conversion fails.
pub async fn get_current_sequencer_commitment(
    chain_id: u64,
    fallback: bool,
) -> (SequencerCommitment, u64) {
    let (chain_name, is_testnet) = get_chain_params(chain_id);
    let req = get_sequencer_request_url(chain_name, fallback, is_testnet);

    let commitment = reqwest::get(req)
        .await
        .expect("Failed to fetch sequencer commitment")
        .json::<SequencerCommitment>()
        .await
        .expect("Failed to parse sequencer commitment JSON");

    let block = ExecutionPayload::try_from(&commitment)
        .expect("Failed to convert commitment to execution payload")
        .block_number;

    (commitment, block)
}

/// Retrieves L1 block information for L2 chains.
///
/// This function queries the L1Block contract on L2 chains to get L1 block information.
/// It makes two calls: one to get the L1 block hash and another to get the L1 block number.
/// This information is used for L1 inclusion proofs.
///
/// # Arguments
/// * `block` - Block number or tag to query.
/// * `chain_id` - Chain ID (Optimism, Base, or their Sepolia variants).
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(EvmInput<EthEvmFactory>, u64)` - Tuple of L1 block input and block number.
///
/// # Panics
/// Panics if:
/// - Invalid chain ID is provided.
/// - RPC calls fail.
/// - Environment building fails.
/// - Contract calls fail.
pub async fn get_l1block_call_input(
    block: BlockNumberOrTag,
    chain_id: u64,
    fallback: bool,
) -> (EvmInput<EthEvmFactory>, u64) {
    // Get the chain name and testnet status for the RPC URL
    let (chain_name, is_testnet) = get_chain_params(chain_id);
    let rpc_url = get_rpc_url(chain_name, fallback, is_testnet);
    let mut env = EthEvmEnv::builder()
        .rpc(Url::parse(rpc_url).expect("Failed to parse RPC URL"))
        .block_number_or_tag(block)
        .chain_spec(&ETH_MAINNET_CHAIN_SPEC)
        .build()
        .await
        .expect("Failed to build EVM environment");

    // Call the L1Block contract to get the L1 block hash
    let call = IL1Block::hashCall {};
    let mut contract = Contract::preflight(L1_BLOCK_ADDRESS_OPSTACK, &mut env);
    contract
        .call_builder(&call)
        .call()
        .await
        .expect("Failed to call L1Block hash");

    // Convert the environment to input for the ZKVM
    let view_call_input_l1_block = env
        .into_input()
        .await
        .expect("Failed to convert environment to input");

    // Call the L1Block contract to get the L1 block number
    let mut env = EthEvmEnv::builder()
        .rpc(Url::parse(rpc_url).expect("Failed to parse RPC URL"))
        .block_number_or_tag(block)
        .chain_spec(&ETH_MAINNET_CHAIN_SPEC)
        .build()
        .await
        .expect("Failed to build EVM environment");

    let call = IL1Block::numberCall {};
    let mut contract = Contract::preflight(L1_BLOCK_ADDRESS_OPSTACK, &mut env);
    let l1_block = contract
        .call_builder(&call)
        .call()
        .await
        .expect("Failed to call L1Block number");

    (view_call_input_l1_block, l1_block)
}

/// Fetches a sequence of blocks for reorg protection.
///
/// This function fetches a sequence of block headers within the reorg protection window
/// to ensure that the blocks used in proofs are sufficiently confirmed and not subject
/// to chain reorganizations.
///
/// The function fetches blocks in parallel for efficiency, starting from
/// `current_block - reorg_protection_depth + 1` up to `current_block`.
///
/// # Arguments
/// * `chain_id` - Chain ID to query.
/// * `rpc_url` - RPC URL for the chain.
/// * `current_block` - Latest block number to start from.
///
/// # Returns
/// * `Vec<RlpHeader<Header>>` - Vector of block headers within the reorg protection window.
///
/// # Panics
/// Panics if:
/// - Invalid chain ID is provided.
/// - RPC calls fail.
/// - Block fetching tasks fail to join.
pub async fn get_linking_blocks(
    chain_id: u64,
    rpc_url: &str,
    current_block: u64,
) -> Vec<RlpHeader<Header>> {
    // Determine the reorg protection depth for the chain
    let reorg_protection_depth = get_reorg_protection_depth(chain_id);

    // Calculate the starting block for the reorg protection window
    let start_block = current_block - reorg_protection_depth + 1;

    // Create futures for parallel block fetching
    let futures: Vec<_> = (start_block..=current_block)
        .map(|block_nr| {
            let rpc_url = rpc_url.to_string();
            tokio::spawn(async move {
                let env = EthEvmEnv::builder()
                    .rpc(Url::parse(&rpc_url).expect("Failed to parse RPC URL"))
                    .block_number_or_tag(BlockNumberOrTag::Number(block_nr))
                    .chain_spec(&ETH_MAINNET_CHAIN_SPEC)
                    .build()
                    .await
                    .expect("Failed to build EVM environment");
                env.header().inner().clone()
            })
        })
        .collect();

    // Execute all futures in parallel and collect results
    join_all(futures)
        .await
        .into_iter()
        .map(|r| r.expect("Failed to join block fetch task"))
        .collect()
}

/// Returns the environment input and L2 block number for Linea L1 call.
///
/// This function queries the Linea L1 message service to get the current L2 block number
/// that corresponds to the given L1 block. This is used for L1 inclusion proofs on Linea.
///
/// # Arguments
/// * `chain_id` - The chain ID to query (must be a Linea chain).
/// * `l1_rpc_url` - The L1 RPC URL.
/// * `l1_block` - The L1 block number to query at.
///
/// # Returns
/// * `(Option<EvmInput<EthEvmFactory>>, Option<u64>)` - The environment input and L2 block number, if available.
///
/// # Panics
/// Panics if:
/// - Invalid chain ID is provided (not a Linea chain).
/// - RPC calls fail.
/// - Environment building fails.
pub async fn get_env_input_for_linea_l1_call(
    chain_id: u64,
    l1_rpc_url: &str,
    l1_block: u64,
) -> (Option<EvmInput<EthEvmFactory>>, Option<u64>) {
    // Select the correct message service address for the chain
    let message_service_address = match chain_id {
        LINEA_CHAIN_ID => L1_MESSAGE_SERVICE_LINEA,
        LINEA_SEPOLIA_CHAIN_ID => L1_MESSAGE_SERVICE_LINEA_SEPOLIA,
        _ => panic!("Invalid chain ID"),
    };

    // Build the Ethereum environment for the L1 block
    let mut env = EthEvmEnv::builder()
        .rpc(Url::parse(l1_rpc_url).expect("Failed to parse RPC URL"))
        .block_number_or_tag(BlockNumberOrTag::Number(l1_block))
        .chain_spec(&ETH_MAINNET_CHAIN_SPEC)
        .build()
        .await
        .expect("Failed to build EVM environment");

    // Make single multicall to get the current L2 block number
    let current_l2_block_number_call = IL1MessageService::currentL2BlockNumberCall {};

    let mut contract = Contract::preflight(message_service_address, &mut env);
    let returns = contract
        .call_builder(&current_l2_block_number_call)
        .call()
        .await
        .expect("Failed to execute current l2 block number call");

    let l2_block_number: u64 = U64::from(returns).try_into().unwrap();

    (
        Some(
            env.into_input()
                .await
                .expect("Failed to convert environment to input"),
        ),
        Some(l2_block_number),
    )
}

/// Helper function to get chain parameters from chain ID.
///
/// Maps a chain ID to its corresponding chain name and testnet status.
///
/// # Arguments
/// * `chain_id` - The chain ID to look up.
///
/// # Returns
/// * `(&'static str, bool)` - Tuple of (chain_name, is_testnet).
///
/// # Panics
/// Panics if an invalid chain ID is provided.
///
/// # Supported Chains
/// - Ethereum mainnet and Sepolia
/// - Optimism mainnet and Sepolia
/// - Base mainnet and Sepolia
/// - Linea mainnet and Sepolia
fn get_chain_params(chain_id: u64) -> (&'static str, bool) {
    match chain_id {
        BASE_CHAIN_ID => ("BASE", false),
        OPTIMISM_CHAIN_ID => ("OPTIMISM", false),
        LINEA_CHAIN_ID => ("LINEA", false),
        ETHEREUM_CHAIN_ID => ("ETHEREUM", false),
        OPTIMISM_SEPOLIA_CHAIN_ID => ("OPTIMISM", true),
        BASE_SEPOLIA_CHAIN_ID => ("BASE", true),
        LINEA_SEPOLIA_CHAIN_ID => ("LINEA", true),
        ETHEREUM_SEPOLIA_CHAIN_ID => ("ETHEREUM", true),
        _ => panic!("Invalid chain ID: {}", chain_id),
    }
}

/// Helper function to get OpStack chain configuration.
///
/// Returns the RPC URLs, portal address, and chain name for OpStack chains.
///
/// # Arguments
/// * `chain_id` - The OpStack chain ID.
/// * `fallback` - Whether to use fallback RPC URLs.
///
/// # Returns
/// * `(&'static str, Address, &'static str, &'static str)` - Tuple of (l1_rpc_url, portal_address, l2_rpc_url, chain_name).
///
/// # Panics
/// Panics if an invalid OpStack chain ID is provided.
///
/// # Supported OpStack Chains
/// - Optimism mainnet and Sepolia
/// - Base mainnet and Sepolia
fn get_opstack_config(
    chain_id: u64,
    fallback: bool,
) -> (&'static str, Address, &'static str, &'static str) {
    let (chain_name, is_testnet) = get_chain_params(chain_id);
    let l1_rpc_url = get_rpc_url("ETHEREUM", fallback, is_testnet);
    let l2_rpc_url = get_rpc_url(chain_name, fallback, is_testnet);

    let portal = match chain_id {
        OPTIMISM_CHAIN_ID => OPTIMISM_PORTAL,
        OPTIMISM_SEPOLIA_CHAIN_ID => OPTIMISM_SEPOLIA_PORTAL,
        BASE_CHAIN_ID => BASE_PORTAL,
        BASE_SEPOLIA_CHAIN_ID => BASE_SEPOLIA_PORTAL,
        _ => panic!("Invalid OpStack chain ID: {}", chain_id),
    };

    (l1_rpc_url, portal, l2_rpc_url, chain_name)
}

/// Helper function to get portal address for a chain.
///
/// Returns the portal contract address for OpStack chains.
///
/// # Arguments
/// * `chain_id` - The chain ID to look up.
///
/// # Returns
/// * `Address` - The portal contract address.
///
/// # Panics
/// Panics if an invalid chain ID is provided.
///
/// # Supported Chains
/// - Optimism mainnet and Sepolia
/// - Base mainnet and Sepolia
fn get_portal_address(chain_id: u64) -> Address {
    match chain_id {
        OPTIMISM_SEPOLIA_CHAIN_ID => OPTIMISM_SEPOLIA_PORTAL,
        BASE_SEPOLIA_CHAIN_ID => BASE_SEPOLIA_PORTAL,
        OPTIMISM_CHAIN_ID => OPTIMISM_PORTAL,
        BASE_CHAIN_ID => BASE_PORTAL,
        _ => panic!("Invalid chain ID for portal: {}", chain_id),
    }
}

/// Helper function to check if a chain is an OpStack chain.
///
/// Determines whether a given chain ID corresponds to an OpStack L2 chain.
///
/// # Arguments
/// * `chain_id` - The chain ID to check.
///
/// # Returns
/// * `bool` - True if the chain is an OpStack chain, false otherwise.
///
/// # Supported OpStack Chains
/// - Optimism mainnet and Sepolia
/// - Base mainnet and Sepolia
fn is_opstack_chain(chain_id: u64) -> bool {
    matches!(
        chain_id,
        OPTIMISM_CHAIN_ID | BASE_CHAIN_ID | OPTIMISM_SEPOLIA_CHAIN_ID | BASE_SEPOLIA_CHAIN_ID
    )
}

/// Helper function to check if a chain is a Linea chain.
///
/// Determines whether a given chain ID corresponds to a Linea L2 chain.
///
/// # Arguments
/// * `chain_id` - The chain ID to check.
///
/// # Returns
/// * `bool` - True if the chain is a Linea chain, false otherwise.
///
/// # Supported Linea Chains
/// - Linea mainnet and Sepolia
fn is_linea_chain(chain_id: u64) -> bool {
    matches!(chain_id, LINEA_CHAIN_ID | LINEA_SEPOLIA_CHAIN_ID)
}

/// Helper function to check if a chain is an Ethereum chain.
///
/// Determines whether a given chain ID corresponds to an Ethereum L1 chain.
///
/// # Arguments
/// * `chain_id` - The chain ID to check.
///
/// # Returns
/// * `bool` - True if the chain is an Ethereum chain, false otherwise.
///
/// # Supported Ethereum Chains
/// - Ethereum mainnet and Sepolia
fn is_ethereum_chain(chain_id: u64) -> bool {
    matches!(chain_id, ETHEREUM_CHAIN_ID | ETHEREUM_SEPOLIA_CHAIN_ID)
}

/// Helper function to get the default sequencer commitment chain for a given chain.
///
/// Returns the default chain ID to use for sequencer commitment queries based on whether
/// the target chain is a testnet or mainnet.
///
/// # Arguments
/// * `_chain_id` - The chain ID (unused, kept for interface consistency).
/// * `is_sepolia` - Whether the chain is a Sepolia testnet variant.
///
/// # Returns
/// * `u64` - The default sequencer commitment chain ID.
///
/// # Default Chains
/// - Sepolia: Optimism Sepolia
/// - Mainnet: Optimism mainnet
fn get_default_sequencer_chain(_chain_id: u64, is_sepolia: bool) -> u64 {
    if is_sepolia {
        OPTIMISM_SEPOLIA_CHAIN_ID
    } else {
        OPTIMISM_CHAIN_ID
    }
}

/// Helper function to get reorg protection depth for a chain.
///
/// Returns the number of blocks to look back for reorg protection based on the chain type.
/// This ensures that blocks used in proofs are sufficiently confirmed to avoid chain reorganizations.
///
/// # Arguments
/// * `chain_id` - The chain ID to get the protection depth for.
///
/// # Returns
/// * `u64` - The reorg protection depth in blocks.
///
/// # Panics
/// Panics if an invalid chain ID is provided.
///
/// # Protection Depths
/// Different chains have different protection depths based on their finality characteristics:
/// - Ethereum: Higher depth due to longer finality
/// - L2 chains: Lower depth due to faster finality
/// - Testnets: Lower depth for faster testing
fn get_reorg_protection_depth(chain_id: u64) -> u64 {
    match chain_id {
        OPTIMISM_CHAIN_ID => REORG_PROTECTION_DEPTH_OPTIMISM,
        BASE_CHAIN_ID => REORG_PROTECTION_DEPTH_BASE,
        LINEA_CHAIN_ID => REORG_PROTECTION_DEPTH_LINEA,
        ETHEREUM_CHAIN_ID => REORG_PROTECTION_DEPTH_ETHEREUM,
        OPTIMISM_SEPOLIA_CHAIN_ID => REORG_PROTECTION_DEPTH_OPTIMISM_SEPOLIA,
        BASE_SEPOLIA_CHAIN_ID => REORG_PROTECTION_DEPTH_BASE_SEPOLIA,
        LINEA_SEPOLIA_CHAIN_ID => REORG_PROTECTION_DEPTH_LINEA_SEPOLIA,
        ETHEREUM_SEPOLIA_CHAIN_ID => REORG_PROTECTION_DEPTH_ETHEREUM_SEPOLIA,
        _ => panic!("invalid chain id"),
    }
}

