[package]
name = "malda_rs"
version = "0.1.0"
edition = "2021"

[dependencies]
alloy = { workspace = true }
alloy-primitives = { workspace = true }
alloy-sol-types = { workspace = true }
anyhow = { workspace = true }
ethers = { workspace = true }
log = { workspace = true }
risc0-ethereum-contracts = { workspace = true }
risc0-steel = { workspace = true, features = ["host"] }
risc0-op-steel = { workspace = true , features = ["host"]}
risc0-zkvm = { workspace = true, features = ["client", "unstable", "bonsai"] }
tracing-subscriber = { workspace = true }
url = { workspace = true }
alloy-consensus = { workspace = true }
serde = { workspace = true }
k256 = { workspace = true }
eyre = { workspace = true }
snap = { workspace = true }
ssz_types = { workspace = true }
ethereum_ssz_derive = { workspace = true }
ethereum_ssz = { workspace = true }
alloy-rlp = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
tree_hash = { workspace = true }
revm = { workspace = true }
bytemuck = { workspace = true }
hex = { workspace = true }


# alloy-primitives-old = { workspace = true }
# consensus-core = { workspace = true }
# consensus = { workspace = true }

reqwest = { version = "0.12.4", features = ["json"] }
bonsai-sdk =  "1.2.4"
bincode = "1.3.3"
tracing = "0.1.39"
dotenvy = "0.15"
boundless-market = "0.10"
clap = { version = "4.5", features = ["derive", "env"] }

malda_utils = { path = "../malda_utils" }