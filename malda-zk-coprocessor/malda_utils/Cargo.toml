[package]
name = "malda_utils"
version = "0.1.0"
edition = "2021"


[dependencies]
alloy-primitives = { workspace = true }
alloy-sol-types = { workspace = true }
risc0-steel = { workspace = true }
risc0-zkvm = { workspace = true, default-features = false, features = ['std', 'unstable'] }
risc0-op-steel = { workspace = true }
serde = { workspace = true }
url = { workspace = true }
hex = { workspace = true }
k256 = { workspace = true }
eyre = { workspace = true }
snap = { workspace = true }
ssz_types = { workspace = true }
ethereum_ssz_derive = { workspace = true }
ethereum_ssz = { workspace = true }
alloy-rlp = { workspace = true }
alloy-consensus = { workspace = true }
revm = { workspace = true }

# alloy-primitives-old = { workspace = true }
# consensus-core = { workspace = true }
tree_hash = { workspace = true }
derive_more = { workspace = true }



