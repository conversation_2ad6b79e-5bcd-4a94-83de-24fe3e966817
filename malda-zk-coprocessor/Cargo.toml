[workspace]
resolver = "2"
members = ["malda_rs", "methods", "patch/ethereum_hashing", "malda_utils" ]
exclude = ["lib"]

[workspace.package]
version = "0.1.0"
edition = "2021"

[workspace.dependencies]
alloy-primitives = { version = "1.0", default-features = false, features = [
    "rlp",
    "serde",
    "std",
] }
alloy-sol-types = { version = "1.0" }
anyhow = { version = "1.0.75" }
bincode = { version = "1.3" }
bytemuck = { version = "1.16" }
ethers = { version = "2.0" }
hex = { version = "0.4" }
log = { version = "0.4" }
methods = { path = "./methods" }
risc0-build = { version = "2.3.1", features = ["docker", "unstable"] }
risc0-build-ethereum = { git = "https://github.com/risc0/risc0-ethereum", tag = "v2.2.0" }
risc0-ethereum-contracts = { git = "https://github.com/risc0/risc0-ethereum", tag = "v2.2.0" }
risc0-steel = { git = "https://github.com/risc0/risc0-ethereum", tag = "v2.2.0" }
risc0-op-steel = { git = "https://github.com/risc0/risc0-ethereum", tag = "v2.2.0" }
risc0-zkvm = { version = "2.3.1", default-features = false, features = ['unstable'] }
risc0-zkp = { version = "2.0.1", default-features = false }
risc0-core = { version = "2.0.0" }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tokio = { version = "1.0", features = ["full"] }
rand = "0.8"
alloy = { version = "1.0", features = ["full"] }
alloy-consensus = "1.0"
alloy-rlp = "0.3.8"
k256 = { version = "0.13.3", features = ["arithmetic", "serde", "expose-field", "std", "ecdsa"] }
eyre = "0.6.8"
serde = { version = "1.0.143", features = ["derive"] }
url = "2.5"
snap = "1"
ssz_types = "0.11.0"
ethereum_ssz_derive = "0.9.0"
ethereum_ssz = "0.9.0"
revm = { version = "25.0", default-features = false }
futures = "0.3"
tree_hash = "0.7.0"
derive_more = { version = "1.0.0", features = ["display"] }
malda_utils = { path = "./malda_utils" }
malda_rs = { path = "./malda_rs" }

socket2 = "0.5.10"
mio = "0.9" 


