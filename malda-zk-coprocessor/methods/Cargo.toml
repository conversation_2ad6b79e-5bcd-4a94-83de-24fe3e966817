[package]
name = "methods"
version = { workspace = true }
edition = { workspace = true }

[package.metadata.risc0]
methods = ["guest"]

[build-dependencies]
hex = { workspace = true }
risc0-build = { workspace = true }
risc0-build-ethereum = { workspace = true }
risc0-zkp = { workspace = true }

[dev-dependencies]
alloy = { workspace = true }
alloy-primitives = { workspace = true }
risc0-steel = { workspace = true }
malda_rs = { workspace = true }
tokio = { workspace = true, features = ["full", "test-util"] }
rand = { workspace = true }
hex = { workspace = true }

