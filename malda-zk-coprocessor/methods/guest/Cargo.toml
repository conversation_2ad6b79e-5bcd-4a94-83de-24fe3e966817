[package]
name = "guests"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "get-proof-data"
path = "src/bin/get_proof_data.rs"

[workspace]

[dependencies]
alloy-primitives = { version = "1.0", default-features = false}
alloy-sol-types = { version = "1.0" }
risc0-steel = { git = "https://github.com/risc0/risc0-ethereum", tag = "v2.2.0" }
risc0-op-steel = { git = "https://github.com/risc0/risc0-ethereum", tag = "v2.2.0" }
risc0-zkvm = { version = "2.3.1", default-features = false, features = ['std', 'unstable'] }
revm = { version = "25.0", default-features = false }
malda_utils = { path = "../../malda_utils" }
alloy-consensus = "1.0"
tiny-keccak = { version = "2.0.2", features = ["keccak"] }
k256 = "0.13.4"
bls12_381 = "0.8.0"

[patch.crates-io]
ethereum_hashing = { path = "../../patch/ethereum_hashing" }
crypto-bigint = { git = "https://github.com/risc0/RustCrypto-crypto-bigint", tag = "v0.5.5-risczero.0" }
k256 = { git = "https://github.com/risc0/RustCrypto-elliptic-curves", tag = "k256/v0.13.4-risczero.1"  }
tiny-keccak = { git = "https://github.com/risc0/tiny-keccak", tag = "tiny-keccak/v2.0.2-risczero.0" }
bls12_381 = { git = "https://github.com/risc0/zkcrypto-bls12_381", tag = "bls12_381/v0.8.0-risczero.0" }

[profile.release]
lto = "thin"
