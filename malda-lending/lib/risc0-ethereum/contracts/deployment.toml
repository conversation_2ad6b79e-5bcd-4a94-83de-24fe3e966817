[chains.ethereum-mainnet]
name = "Ethereum Mainnet"
id = 1
etherscan-url = "https://etherscan.io/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

# TODO: Add information for 1.0 contracts.
[[chains.ethereum-mainnet.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.ethereum-mainnet.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.ethereum-sepolia]
name = "Ethereum Sepolia"
id = ********
etherscan-url = "https://sepolia.etherscan.io/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.ethereum-sepolia.verifiers]]
version = "1.1.0-rc.2"
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.ethereum-sepolia.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.ethereum-holesky]
name = "Ethereum Holesky"
id = 17000
etherscan-url = "https://holesky.etherscan.io/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.ethereum-holesky.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.ethereum-holesky.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.arbitrum-mainnet]
name = "Arbitrum Mainnet"
id = 42161
etherscan-url = "https://arbiscan.io/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.arbitrum-mainnet.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.arbitrum-mainnet.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.arbitrum-sepolia]
name = "Arbitrum Sepolia"
id = 421614
etherscan-url = "https://sepolia.arbiscan.io/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.arbitrum-sepolia.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.arbitrum-sepolia.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.avalanche-mainnet]
name = "Avalanche Mainnet"
id = 43114
etherscan-url = "https://snowtrace.io"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.avalanche-mainnet.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.avalanche-mainnet.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.avalanche-fuji]
name = "Avalanche Fuji"
id = 43113
etherscan-url = "https://testnet.snowtrace.io"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.avalanche-fuji.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.avalanche-fuji.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.base-mainnet]
name = "Base Mainnet"
id = 8453
etherscan-url = "https://basescan.org/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.base-mainnet.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.base-mainnet.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.base-sepolia]
name = "Base Sepolia"
id = 84532
etherscan-url = "https://sepolia.basescan.org/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.base-sepolia.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.base-sepolia.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.optimism-mainnet]
name = "Optimism Mainnet"
id = 10
etherscan-url = "https://optimistic.etherscan.io/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.optimism-mainnet.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.optimism-mainnet.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.optimism-sepolia]
name = "Optimism Sepolia"
id = 11110
etherscan-url = "https://sepolia-optimism.etherscan.io/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

# NOTE: 1.0 was not deployed to OP Sepolia.

[[chains.optimism-sepolia.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.optimism-sepolia.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.linea-mainnet]
name = "Linea Mainnet"
id = 59144
etherscan-url = "https://lineascan.build/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.linea-mainnet.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.linea-mainnet.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.linea-sepolia]
name = "Linea Sepolia"
id = 59141
etherscan-url = "https://sepolia.lineascan.build/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

# NOTE: As of the 1.1.0-rc.2 deployment on August 21, Forge could not verify the contracts.
# Apperent issue is that because Linea Sepolia is not in the list of chains named in alloy-chains, Forge doesn't know its Etherscan API URL.

# NOTE: As of the 1.1.0-rc.3 Deployment on August 29, Fireblocks does not support sending transactions to Linea Sepolia.
# As a result, the router is deployed, but it is empty and useless.

[[chains.linea-sepolia.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.linea-sepolia.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.polygon-zkevm-mainnet]
name = "Polygon zkEVM Mainnet"
id = 1101
etherscan-url = "https://zkevm.polygonscan.com/"

# Accounts
admin = "******************************************"

# Contracts
timelock-controller = "******************************************"
router = "******************************************"

[[chains.polygon-zkevm-mainnet.verifiers]]
selector = "0x4c630d87"
verifier = "******************************************"
estop = "******************************************"

[[chains.polygon-zkevm-mainnet.verifiers]]
version = "1.1.0-rc.3"
selector = "0x50bd1769"
verifier = "******************************************"
estop = "******************************************"

###

[chains.polygon-zkevm-testnet]
name = "Polygon zkEVM Testnet"
id = 1442
etherscan-url = "https://cardona-zkevm.polygonscan.com/"

# Accounts
admin = "******************************************"

# Contracts
# TODO: Deploy TimelockController and router here?

# NOTE: As of the 1.1.0-rc.2 deployment on August 21, deploying to polygon zkEVM testnet failed.
# On Polygon zkEVM Cardona testnet, gave an error of "Failed to get EIP-1559 fees".
