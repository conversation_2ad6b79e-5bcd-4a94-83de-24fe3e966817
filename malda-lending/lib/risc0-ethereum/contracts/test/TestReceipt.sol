// Copyright 2024 RISC Zero, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0

// This file is automatically generated by:
// cargo xtask bootstrap-groth16

pragma solidity ^0.8.13;

library TestReceipt {
    bytes public constant SEAL =
        hex"50bd1769096d29a4e342d93785757cde64ef07c09f317481f0ee9274f14281dc501c1b2e036ee070b7bd75b4f0253f7349afaa4074d73f77b09de60dd82d3fbeba8cc4a10dab619b389ed53ddfc3113e055729ff430a82f57d7edc24821e782653b9f1ba00558126e75bcb392a9a58d45af8489f4441d77e91d10c11dcea70c33c93f3ba03dab52a25735bb04f2526ec7289c1ee8912f921c4f5d380a5f906782f60044a0d44d7005528e1821e458e7bf108777452b2327ba1998710aa62e1e106858a302c0fe02760c5fda0000e039d263b2cc918eb2539da008bbbe7007f767d45d22d18f589ab466da35e0d0bfc300af4b0bc941a9897a863b48a2deb5f057c2f512c";
    bytes public constant JOURNAL = hex"6a75737420612073696d706c652072656365697074";
    bytes32 public constant IMAGE_ID = hex"d01c15afa768a05b213a9e5fcdcc5724a2947e00098c7ec34ccbe2946bbc0013";
}
