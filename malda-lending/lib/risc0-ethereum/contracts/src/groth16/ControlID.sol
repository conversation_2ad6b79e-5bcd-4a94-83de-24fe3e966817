// Copyright 2024 RISC Zero, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0

// This file is automatically generated by:
// cargo xtask bootstrap-groth16

pragma solidity ^0.8.9;

library ControlID {
    bytes32 public constant CONTROL_ROOT = hex"8b6dcf11d463ac455361b41fb3ed053febb817491bdea00fdb340e45013b852e";
    // NOTE: This has the opposite byte order to the value in the risc0 repository.
    bytes32 public constant BN254_CONTROL_ID = hex"05a022e1db38457fb510bc347b30eb8f8cf3eda95587653d0eac19e1f10d164e";
}
