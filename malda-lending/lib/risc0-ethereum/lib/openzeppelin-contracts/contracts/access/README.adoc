= Access Control

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/access

This directory provides ways to restrict who can access the functions of a contract or when they can do it.

- {AccessControl} provides a general role based access control mechanism. Multiple hierarchical roles can be created and assigned each to multiple accounts.
- {Ownable} is a simpler mechanism with a single owner "role" that can be assigned to a single account. This simpler mechanism can be useful for quick tests but projects with production concerns are likely to outgrow it.

== Core

{{Ownable}}

{{Ownable2Step}}

{{IAccessControl}}

{{AccessControl}}

== Extensions

{{IAccessControlEnumerable}}

{{AccessControlEnumerable}}

{{IAccessControlDefaultAdminRules}}

{{AccessControlDefaultAdminRules}}

== AccessManager

{{IAuthority}}

{{IAccessManager}}

{{AccessManager}}

{{IAccessManaged}}

{{AccessManaged}}

{{AuthorityUtils}}
