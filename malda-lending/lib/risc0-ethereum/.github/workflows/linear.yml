name: Find or Create Linear Issue for PR

on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
    types: ["opened", "edited", "reopened", "synchronize"]

permissions:
  pull-requests: write
  repository-projects: read

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: false

jobs:
  create-linear-issue-pr:
    runs-on: ubuntu-latest
    steps:
      - name: Find or create a Linear Issue
        uses: risc0/action-find-or-create-linear-issue@risc0
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          linear-api-key: ${{ secrets.LINEAR_API_KEY }}
          linear-team-key: "WEB3"
          linear-created-issue-state-id: "2505ebd6-1fbe-4b25-b2a8-792dfaa50ad9" # in progress
