[package]
name = "apps"
version = { workspace = true }
edition = { workspace = true }

[dependencies]
alloy = { version = "0.4", features = ["full"] }
alloy-primitives = { workspace = true }
alloy-sol-types = { workspace = true }
anyhow = { workspace = true }
clap = { version = "4.5", features = ["derive", "env"] }
env_logger = { version = "0.10" }
governance-methods = { workspace = true }
hex = { workspace = true }
log = { workspace = true }
risc0-ethereum-contracts = { workspace = true }
risc0-zkvm = { workspace = true, features = ["client"] }
tokio = { workspace = true, features = ["full"] }
tracing-subscriber = { workspace = true }
url = { workspace = true }

