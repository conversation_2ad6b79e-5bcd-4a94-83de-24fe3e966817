[package]
name = "governance-methods"
version = { workspace = true }
edition = { workspace = true }

[package.metadata.risc0]
methods = ["guest"]

[build-dependencies]
risc0-build = { workspace = true }
risc0-build-ethereum = { workspace = true }
risc0-zkp = { workspace = true }

[dev-dependencies]
alloy-primitives = { workspace = true }
alloy-sol-types = { workspace = true }
hex = { workspace = true }
risc0-zkvm = { workspace = true, features = ["client"] }
