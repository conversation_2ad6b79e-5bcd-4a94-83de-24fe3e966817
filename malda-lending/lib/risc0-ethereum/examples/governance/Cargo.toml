[workspace]
resolver = "2"
members = ["apps", "methods"]
exclude = ["lib"]

[workspace.package]
version = "0.1.0"
edition = "2021"

[workspace.dependencies]
# Intra-workspace dependencies
risc0-build-ethereum = { path = "../../build" }
risc0-ethereum-contracts = { path = "../../contracts" }

# risc0 monorepo dependencies.
risc0-build = { version = "1.1.0", features = ["docker"] }
risc0-zkvm = { version = "1.1.0", default-features = false }
risc0-zkp = { version = "1.1.0", default-features = false }

alloy-primitives = { version = "0.8", features = ["rlp", "serde", "std"] }
alloy-sol-types = { version = "0.8" }
anyhow = { version = "1.0.75" }
bincode = { version = "1.3" }
bytemuck = { version = "1.14" }
hex = { version = "0.4" }
log = { version = "0.4" }
governance-methods = { path = "./methods" }
serde = { version = "1.0", features = ["derive", "std"] }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tokio = { version = "1.39", features = ["full"] }
url = { version = "2.5" }

[profile.release]
debug = 1
lto = true
