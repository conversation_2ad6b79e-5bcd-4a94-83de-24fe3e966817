[package]
name = "apps"
version = { workspace = true }
edition = { workspace = true }

[dependencies]
alloy = { workspace = true }
alloy-primitives = { workspace = true }
anyhow = { workspace = true }
clap = { workspace = true, features = ["derive", "env"] }
erc20-counter-methods = { workspace = true }
log = { workspace = true }
risc0-ethereum-contracts = { workspace = true }
risc0-steel = { workspace = true, features = ["host"] }
risc0-zkvm = { workspace = true, features = ["client"] }
tokio = { workspace = true }
tracing-subscriber = { workspace = true }
url = { workspace = true }
