[workspace]
resolver = "2"
members = ["core", "host", "methods"]

[workspace.dependencies]
# Intra-workspace dependencies
risc0-steel = { path = "../../steel" }

# risc0 monorepo dependencies.
risc0-build = { version = "1.1.2" }
risc0-zkvm = { version = "1.1.2", default-features = false }
risc0-zkp = { version = "1.1.2", default-features = false }

alloy-primitives = { version = "0.8", features = ["serde", "rlp", "std"] }
alloy-rlp = { version = "0.3", default-features = false }
alloy-rlp-derive = { version = "0.3", default-features = false }
alloy-sol-types = { version = "0.8" }
anyhow = "1.0"
clap = { version = "4.4", features = ["derive", "env"] }
log = "0.4"
token-stats-core = { path = "core" }
token-stats-methods = { path = "methods" }
once_cell = "1.19"
rlp = "0.5.2"
serde = "1.0"
thiserror = "1.0"
tokio = { version = "1.35", features = ["full"] }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
url = { version = "2.5" }

# Always optimize; building and running the guest takes much longer without optimization.
[profile.dev]
opt-level = 3

[profile.dev.build-override]
opt-level = 3

[profile.release]
debug = 1
lto = true

[profile.release.build-override]
opt-level = 3
