[package]
name = "host"
version = "0.1.0"
edition = "2021"

[dependencies]
alloy-sol-types = { workspace = true }
anyhow = { workspace = true }
clap = { workspace = true }
risc0-steel = { workspace = true, features = ["host"] }
risc0-zkvm = { workspace = true, features = ["client"] }
token-stats-core = { workspace = true }
token-stats-methods = { workspace = true }
tokio = { workspace = true }
tracing-subscriber = { workspace = true }
url = { workspace = true }
