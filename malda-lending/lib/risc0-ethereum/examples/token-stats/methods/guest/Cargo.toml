[package]
name = "token-stats"
version = "0.1.0"
edition = "2021"

[workspace]

[dependencies]
alloy-sol-types = { version = "0.8" }
risc0-steel = { path = "../../../../steel" }
risc0-zkvm = { version = "1.1.2", default-features = false, features = ["std"] }
token-stats-core = { path = "../../core" }

[patch.crates-io]
# use optimized risc0 circuit
crypto-bigint = { git = "https://github.com/risc0/RustCrypto-crypto-bigint", tag = "v0.5.5-risczero.0" }
k256 = { git = "https://github.com/risc0/RustCrypto-elliptic-curves", tag = "k256/v0.13.3-risczero.0" }
sha2 = { git = "https://github.com/risc0/RustCrypto-hashes", tag = "sha2-v0.10.8-risczero.0" }
