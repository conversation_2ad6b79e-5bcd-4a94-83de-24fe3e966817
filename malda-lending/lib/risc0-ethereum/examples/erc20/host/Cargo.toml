[package]
name = "host"
version = "0.1.0"
edition = "2021"

[dependencies]
alloy-primitives = { workspace = true }
alloy-sol-types = { workspace = true }
anyhow = { workspace = true }
clap = { workspace = true }
erc20-methods = { workspace = true }
risc0-steel = { workspace = true, features = ["host"] }
risc0-zkvm = { workspace = true, features = ["client"] }
tokio = { workspace = true }
tracing-subscriber = { workspace = true }
url = { workspace = true }
