[workspace]
resolver = "2"
members = ["host", "methods"]

[workspace.dependencies]
# Intra-workspace dependencies
risc0-steel = { path = "../../steel" }

# risc0 monorepo dependencies.
risc0-build = { version = "1.1.2" }
risc0-zkvm = { version = "1.1.2", default-features = false }
risc0-zkp = { version = "1.1.2", default-features = false }

alloy-primitives = { version = "0.8" }
alloy-sol-types = { version = "0.8" }
anyhow = "1.0"
clap = { version = "4.5", features = ["derive", "env"] }
erc20-methods = { path = "methods" }
tokio = { version = "1.39", features = ["full"] }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
url = { version = "2.5" }

# Always optimize; building and running the guest takes much longer without optimization.
[profile.dev]
opt-level = 3

[profile.dev.build-override]
opt-level = 3

[profile.release]
debug = 1
lto = true

[profile.release.build-override]
opt-level = 3
