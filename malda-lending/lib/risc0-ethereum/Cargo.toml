[workspace]
resolver = "2"
members = ["build", "contracts", "ffi", "ffi/guests", "steel"]

[workspace.package]
version = "1.1.4"
edition = "2021"
license = "Apache-2.0"
homepage = "https://risczero.com/"
repository = "https://github.com/risc0/risc0-ethereum/"

[workspace.dependencies]
# Intra-workspace dependencies
risc0-build-ethereum = { version = "1.1.4", default-features = false, path = "build" }
risc0-ethereum-contracts = { version = "1.1.4", default-features = false, path = "contracts" }
risc0-steel = { version = "1.1.4", default-features = false, path = "steel" }
risc0-forge-ffi = { version = "1.1.4", default-features = false, path = "ffi" }

# risc0 monorepo dependencies.
risc0-build = { version = "1.1.2", default-features = false }
risc0-zkp = { version = "1.1.2", default-features = false }
risc0-zkvm = { version = "1.1.2", default-features = false }

# Alloy guest dependencies
alloy-consensus = { version = "0.4" }
alloy-primitives = { version = "=0.8.5" }
alloy-rlp = { version = "0.3.8" }
alloy-rlp-derive = { version = "0.3.8" }
alloy-sol-types = { version = "=0.8.5" }

# Alloy host dependencies
alloy = { version = "0.4" }
alloy-trie = { version = "0.6" }

# Beacon chain support
beacon-api-client = { git = "https://github.com/ralexstokes/ethereum-consensus.git", rev = "cf3c404043230559660810bc0c9d6d5a8498d819" }
ethereum-consensus = { git = "https://github.com/ralexstokes/ethereum-consensus.git", rev = "cf3c404043230559660810bc0c9d6d5a8498d819" }

anyhow = { version = "1.0" }
bincode = { version = "1.3" }
clap = { version = "4.5", features = ["derive", "env"] }
log = "0.4"
nybbles = { version = "0.2.1" }
once_cell = "1.19"
revm = { version = "14.0", default-features = false, features = ["std"] }
reqwest = "0.12"
serde = "1.0"
serde_json = "1.0"
sha2 = { version = "0.10" }
thiserror = "1.0"
test-log = "0.2.15"
tokio = { version = "1.35" }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
url = { version = "2.5" }
