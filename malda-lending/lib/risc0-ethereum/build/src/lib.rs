// Copyright 2024 RISC Zero, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use std::{
    env, fs,
    io::Write,
    path::{Path, PathBuf},
    process::{Command, Stdio},
};

use anyhow::{anyhow, bail, Context, Result};
use risc0_build::GuestListEntry;
use risc0_zkp::core::digest::Digest;

const SOL_HEADER: &str = r#"// Copyright 2024 RISC Zero, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0

// This file is automatically generated

"#;

const IMAGE_ID_LIB_HEADER: &str = r#"pragma solidity ^0.8.20;

library ImageID {
"#;

const ELF_LIB_HEADER: &str = r#"pragma solidity ^0.8.20;

library Elf {
"#;

/// Options for building and code generation.
#[derive(Debug, Clone, Default)]
#[non_exhaustive] // more options may be added in the future.
pub struct Options {
    /// Path the generated Solidity file with image ID information.
    pub image_id_sol_path: Option<PathBuf>,

    /// Path the generated Solidity file with ELF information.
    pub elf_sol_path: Option<PathBuf>,
}

// Builder interface is provided to make it easy to add more intelligent default and additional
// options without breaking backwards compatibility in the future.
impl Options {
    /// Add a path to generate the Solidity file with image ID information.
    pub fn with_image_id_sol_path(mut self, path: impl AsRef<Path>) -> Self {
        self.image_id_sol_path = Some(path.as_ref().to_owned());
        self
    }

    /// Add a path to generate the Solidity file with ELF information.
    pub fn with_elf_sol_path(mut self, path: impl AsRef<Path>) -> Self {
        self.elf_sol_path = Some(path.as_ref().to_owned());
        self
    }
}

/// Generate Solidity files for integrating a RISC Zero project with Ethereum.
pub fn generate_solidity_files(guests: &[GuestListEntry], opts: &Options) -> Result<()> {
    // Skip Solidity source files generation if RISC0_SKIP_BUILD is enabled.
    if env::var("RISC0_SKIP_BUILD").is_ok() {
        return Ok(());
    }
    let image_id_file_path = opts
        .image_id_sol_path
        .as_ref()
        .ok_or(anyhow!("path for image ID Solidity file must be provided"))?;
    fs::write(image_id_file_path, generate_image_id_sol(guests)?)
        .with_context(|| format!("failed to save changes to {}", image_id_file_path.display()))?;

    let elf_sol_path = opts.elf_sol_path.as_ref().ok_or(anyhow!(
        "path for guest ELFs Solidity file must be provided"
    ))?;
    fs::write(elf_sol_path, generate_elf_sol(guests)?)
        .with_context(|| format!("failed to save changes to {}", image_id_file_path.display()))?;

    Ok(())
}

/// Generate source code for a Solidity library containing image IDs for the given guest programs.
pub fn generate_image_id_sol(guests: &[GuestListEntry]) -> Result<Vec<u8>> {
    // Assemble a list of image IDs.
    let image_ids: Vec<_> = guests
        .iter()
        .map(|guest| {
            let name = guest.name.to_uppercase().replace('-', "_");
            let image_id = hex::encode(Digest::from(guest.image_id));
            format!("bytes32 public constant {name}_ID = bytes32(0x{image_id});")
        })
        .collect();

    let image_id_lines = image_ids.join("\n");

    // Building the final image_ID file content.
    let file_content = format!("{SOL_HEADER}{IMAGE_ID_LIB_HEADER}\n{image_id_lines}\n}}");
    forge_fmt(file_content.as_bytes()).context("failed to format image ID file")
}

/// Generate source code for a Solidity library containing local paths to the ELF files of guest
/// programs. Note that these paths will only resolve on the build machine, and are intended only
/// for test integration.
pub fn generate_elf_sol(guests: &[GuestListEntry]) -> Result<Vec<u8>> {
    // Assemble a list of paths to ELF files.
    let elf_paths: Vec<_> = guests
        .iter()
        .map(|guest| {
            let name = guest.name.to_uppercase().replace('-', "_");

            let elf_path = guest.path.to_string();
            format!("string public constant {name}_PATH = \"{elf_path}\";")
        })
        .collect();

    // Building the final elf_path file content.
    let elf_path_lines = elf_paths.join("\n");
    let file_content = format!("{SOL_HEADER}{ELF_LIB_HEADER}\n{elf_path_lines}\n}}");
    forge_fmt(file_content.as_bytes()).context("failed to format image ID file")
}

/// Uses forge fmt as a subprocess to format the given Solidity source.
fn forge_fmt(src: &[u8]) -> Result<Vec<u8>> {
    // Spawn `forge fmt`
    let mut fmt_proc = Command::new("forge")
        .args(["fmt", "-", "--raw"])
        .stdin(Stdio::piped())
        .stdout(Stdio::piped())
        .spawn()
        .context("failed to spawn forge fmt")?;

    // Write the source code as bytes to stdin.
    fmt_proc
        .stdin
        .take()
        .context("failed to take forge fmt stdin handle")?
        .write_all(src)
        .context("failed to write to forge fmt stdin")?;

    let fmt_out = fmt_proc
        .wait_with_output()
        .context("failed to run forge fmt")?;

    if !fmt_out.status.success() {
        bail!(
            "forge fmt on image ID file content exited with status {}",
            fmt_out.status,
        );
    }

    Ok(fmt_out.stdout)
}

#[cfg(test)]
mod tests {
    use super::forge_fmt;
    use pretty_assertions::assert_eq;

    // Copied from https://solidity-by-example.org/first-app/
    const FORMATTED_SRC: &str = r#"// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

contract Counter {
    uint256 public count;

    // Function to get the current count
    function get() public view returns (uint256) {
        return count;
    }

    // Function to increment count by 1
    function inc() public {
        count += 1;
    }

    // Function to decrement count by 1
    function dec() public {
        // This function will fail if count = 0
        count -= 1;
    }
}
"#;

    const UNFORMATTED_SRC: &str = r#"
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

contract Counter {
    uint public  count;

    // Function to get the current count
    function get() public view returns (uint) {
        return count;
    }

    // Function to increment count by 1
    function inc()
    public {
        count
         +=
         1;
    }

// Function to decrement count by 1
        function dec() public {
            // This function will fail if count = 0
count-=1;
    }
}

"#;

    #[test]
    fn forge_fmt_works() {
        assert_eq!(
            String::from_utf8(forge_fmt(UNFORMATTED_SRC.as_bytes()).unwrap()).unwrap(),
            String::from_utf8(FORMATTED_SRC.as_bytes().to_owned()).unwrap()
        );
    }
}
