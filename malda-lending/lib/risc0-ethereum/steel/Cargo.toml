[package]
name = "risc0-steel"
description = "Query Ethereum state, or any other EVM-based blockchain state within the RISC Zero zkVM."
version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
alloy = { workspace = true, optional = true, features = ["eips", "network", "provider-http", "rpc-types"] }
alloy-consensus = { workspace = true }
alloy-primitives = { workspace = true, features = ["rlp", "serde"] }
alloy-rlp = { workspace = true }
alloy-rlp-derive = { workspace = true }
alloy-sol-types = { workspace = true }
anyhow = { workspace = true }
ethereum-consensus = { workspace = true, optional = true }
log = { workspace = true, optional = true }
nybbles = { workspace = true, features = ["serde"] }
once_cell = { workspace = true }
reqwest = { workspace = true, optional = true }
revm = { workspace = true, features = ["serde"] }
serde = { workspace = true }
serde_json = { workspace = true, optional = true }
sha2 = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true, optional = true }
url = { workspace = true, optional = true }

[dev-dependencies]
alloy = { workspace = true, features = ["contract", "node-bindings"] }
alloy-trie = { workspace = true }
bincode = { workspace = true }
risc0-steel = { path = ".", features = ["host"] }
serde_json = { workspace = true }
test-log = { workspace = true }

[features]
default = []
host = [
    "dep:alloy",
    "dep:ethereum-consensus",
    "dep:log",
    "dep:reqwest",
    "dep:serde_json",
    "dep:tokio",
    "dep:url",
]
