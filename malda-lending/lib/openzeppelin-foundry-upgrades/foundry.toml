[profile.default]
src = "src"
out = "out"
libs = ["node_modules", "lib"]
build_info = true
extra_output = ["storageLayout"]

[profile.openzeppelin-contracts-v4]
src = "test-profiles/openzeppelin-contracts-v4/src"
test = "test-profiles/openzeppelin-contracts-v4/test"
remappings = [
  "@openzeppelin/contracts/=node_modules/@openzeppelin/contracts-v4",
  "@openzeppelin/contracts-upgradeable/=node_modules/@openzeppelin/contracts-upgradeable-v4"
]

[profile.openzeppelin-contracts-v4-with-v5-proxies]
src = "test-profiles/openzeppelin-contracts-v4-with-v5-proxies/src"
test = "test-profiles/openzeppelin-contracts-v4-with-v5-proxies/test"
remappings = [
  "@openzeppelin/contracts/=node_modules/@openzeppelin/contracts",
  "@openzeppelin/contracts-upgradeable/=node_modules/@openzeppelin/contracts-upgradeable-v4"
]

[profile.build-info-v1]
src = "test-profiles/build-info-v1/src"
test = "test-profiles/build-info-v1/test"

[profile.build-info-v2]
src = "test-profiles/build-info-v2/src"
test = "test-profiles/build-info-v2/test"

[profile.build-info-v2-bad]
src = "test-profiles/build-info-v2-bad/src"
test = "test-profiles/build-info-v2-bad/test"

[profile.build-info-v2-reference-contract]
src = "test-profiles/build-info-v2-reference-contract/src"
test = "test-profiles/build-info-v2-reference-contract/test"