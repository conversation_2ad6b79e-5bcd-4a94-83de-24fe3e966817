:github-icon: pass:[<svg class="icon"><use href="#github-icon"/></svg>]
:xref-Defender-Defender-deployContract-string-: xref:#Defender-Defender-deployContract-string-
:xref-Defender-Defender-deployContract-string-struct-DefenderOptions-: xref:#Defender-Defender-deployContract-string-struct-DefenderOptions-
:xref-Defender-Defender-deployContract-string-bytes-: xref:#Defender-Defender-deployContract-string-bytes-
:xref-Defender-Defender-deployContract-string-bytes-struct-DefenderOptions-: xref:#Defender-Defender-deployContract-string-bytes-struct-DefenderOptions-
:xref-Defender-Defender-proposeUpgrade-address-string-struct-Options-: xref:#Defender-Defender-proposeUpgrade-address-string-struct-Options-
:xref-Defender-Defender-getDeployApprovalProcess--: xref:#Defender-Defender-getDeployApprovalProcess--
:xref-Defender-Defender-getUpgradeApprovalProcess--: xref:#Defender-Defender-getUpgradeApprovalProcess--
:deployContract: pass:normal[xref:#Defender-Defender-deployContract-string-[`++deployContract++`]]
:deployContract: pass:normal[xref:#Defender-Defender-deployContract-string-struct-DefenderOptions-[`++deployContract++`]]
:deployContract: pass:normal[xref:#Defender-Defender-deployContract-string-bytes-[`++deployContract++`]]
:deployContract: pass:normal[xref:#Defender-Defender-deployContract-string-bytes-struct-DefenderOptions-[`++deployContract++`]]
:proposeUpgrade: pass:normal[xref:#Defender-Defender-proposeUpgrade-address-string-struct-Options-[`++proposeUpgrade++`]]
:getDeployApprovalProcess: pass:normal[xref:#Defender-Defender-getDeployApprovalProcess--[`++getDeployApprovalProcess++`]]
:getUpgradeApprovalProcess: pass:normal[xref:#Defender-Defender-getUpgradeApprovalProcess--[`++getUpgradeApprovalProcess++`]]

[.contract]
[[Defender-Defender]]
=== `++Defender++` link:https://github.com/OpenZeppelin/openzeppelin-foundry-upgrades/blob/main/src/Defender.sol[{github-icon},role=heading-link]

[.hljs-theme-light.nopadding]
```solidity
import { Defender } from "openzeppelin-foundry-upgrades/Defender.sol";
```

Library for interacting with OpenZeppelin Defender from Forge scripts or tests.

[.contract-index]
.Functions
--
* {xref-Defender-Defender-deployContract-string-}[`++deployContract(contractName)++`]
* {xref-Defender-Defender-deployContract-string-struct-DefenderOptions-}[`++deployContract(contractName, defenderOpts)++`]
* {xref-Defender-Defender-deployContract-string-bytes-}[`++deployContract(contractName, constructorData)++`]
* {xref-Defender-Defender-deployContract-string-bytes-struct-DefenderOptions-}[`++deployContract(contractName, constructorData, defenderOpts)++`]
* {xref-Defender-Defender-proposeUpgrade-address-string-struct-Options-}[`++proposeUpgrade(proxyAddress, newImplementationContractName, opts)++`]
* {xref-Defender-Defender-getDeployApprovalProcess--}[`++getDeployApprovalProcess()++`]
* {xref-Defender-Defender-getUpgradeApprovalProcess--}[`++getUpgradeApprovalProcess()++`]

--

[.contract-item]
[[Defender-Defender-deployContract-string-]]
==== `[.contract-item-name]#++deployContract++#++(string contractName) → address++` [.item-kind]#internal#

Deploys a contract to the current network using OpenZeppelin Defender.

WARNING: Do not use this function directly if you are deploying an upgradeable contract. This function does not validate whether the contract is upgrade safe.

NOTE: If using an EOA or Safe to deploy, go to https://defender.openzeppelin.com/v2/#/deploy[Defender deploy] to submit the pending deployment while the script is running.
The script waits for the deployment to complete before it continues.

*Parameters:*

* `contractName` (`string`) - Name of the contract to deploy, e.g. "MyContract.sol" or "MyContract.sol:MyContract" or artifact path relative to the project root directory

*Returns*

* (`address`) - Address of the deployed contract

[.contract-item]
[[Defender-Defender-deployContract-string-struct-DefenderOptions-]]
==== `[.contract-item-name]#++deployContract++#++(string contractName, struct DefenderOptions defenderOpts) → address++` [.item-kind]#internal#

Deploys a contract to the current network using OpenZeppelin Defender.

WARNING: Do not use this function directly if you are deploying an upgradeable contract. This function does not validate whether the contract is upgrade safe.

NOTE: If using an EOA or Safe to deploy, go to https://defender.openzeppelin.com/v2/#/deploy[Defender deploy] to submit the pending deployment while the script is running.
The script waits for the deployment to complete before it continues.

*Parameters:*

* `contractName` (`string`) - Name of the contract to deploy, e.g. "MyContract.sol" or "MyContract.sol:MyContract" or artifact path relative to the project root directory
* `defenderOpts` (`struct DefenderOptions`) - Defender deployment options. Note that the `useDefenderDeploy` option is always treated as `true` when called from this function.

*Returns*

* (`address`) - Address of the deployed contract

[.contract-item]
[[Defender-Defender-deployContract-string-bytes-]]
==== `[.contract-item-name]#++deployContract++#++(string contractName, bytes constructorData) → address++` [.item-kind]#internal#

Deploys a contract with constructor arguments to the current network using OpenZeppelin Defender.

WARNING: Do not use this function directly if you are deploying an upgradeable contract. This function does not validate whether the contract is upgrade safe.

NOTE: If using an EOA or Safe to deploy, go to https://defender.openzeppelin.com/v2/#/deploy[Defender deploy] to submit the pending deployment while the script is running.
The script waits for the deployment to complete before it continues.

*Parameters:*

* `contractName` (`string`) - Name of the contract to deploy, e.g. "MyContract.sol" or "MyContract.sol:MyContract" or artifact path relative to the project root directory
* `constructorData` (`bytes`) - Encoded constructor arguments

*Returns*

* (`address`) - Address of the deployed contract

[.contract-item]
[[Defender-Defender-deployContract-string-bytes-struct-DefenderOptions-]]
==== `[.contract-item-name]#++deployContract++#++(string contractName, bytes constructorData, struct DefenderOptions defenderOpts) → address++` [.item-kind]#internal#

Deploys a contract with constructor arguments to the current network using OpenZeppelin Defender.

WARNING: Do not use this function directly if you are deploying an upgradeable contract. This function does not validate whether the contract is upgrade safe.

NOTE: If using an EOA or Safe to deploy, go to https://defender.openzeppelin.com/v2/#/deploy[Defender deploy] to submit the pending deployment while the script is running.
The script waits for the deployment to complete before it continues.

*Parameters:*

* `contractName` (`string`) - Name of the contract to deploy, e.g. "MyContract.sol" or "MyContract.sol:MyContract" or artifact path relative to the project root directory
* `constructorData` (`bytes`) - Encoded constructor arguments
* `defenderOpts` (`struct DefenderOptions`) - Defender deployment options. Note that the `useDefenderDeploy` option is always treated as `true` when called from this function.

*Returns*

* (`address`) - Address of the deployed contract

[.contract-item]
[[Defender-Defender-proposeUpgrade-address-string-struct-Options-]]
==== `[.contract-item-name]#++proposeUpgrade++#++(address proxyAddress, string newImplementationContractName, struct Options opts) → struct ProposeUpgradeResponse++` [.item-kind]#internal#

Proposes an upgrade to an upgradeable proxy using OpenZeppelin Defender.

This function validates a new implementation contract in comparison with a reference contract, deploys the new implementation contract using Defender,
and proposes an upgrade to the new implementation contract using an upgrade approval process on Defender.

Supported for UUPS or Transparent proxies. Not currently supported for beacon proxies or beacons.
For beacons, use `Upgrades.prepareUpgrade` along with a transaction proposal on Defender to upgrade the beacon to the deployed implementation.

Requires that either the `referenceContract` option is set, or the contract has a `@custom:oz-upgrades-from <reference>` annotation.

WARNING: Ensure that the reference contract is the same as the current implementation contract that the proxy is pointing to.
This function does not validate that the reference contract is the current implementation.

NOTE: If using an EOA or Safe to deploy, go to https://defender.openzeppelin.com/v2/#/deploy[Defender deploy] to submit the pending deployment of the new implementation contract while the script is running.
The script waits for the deployment to complete before it continues.

*Parameters:*

* `proxyAddress` (`address`) - The proxy address
* `newImplementationContractName` (`string`) - Name of the new implementation contract to upgrade to, e.g. "MyContract.sol" or "MyContract.sol:MyContract" or artifact path relative to the project root directory
* `opts` (`struct Options`) - Common options. Note that the `defender.useDefenderDeploy` option is always treated as `true` when called from this function.

*Returns*

* (`struct ProposeUpgradeResponse`) - Struct containing the proposal ID and URL for the upgrade proposal

[.contract-item]
[[Defender-Defender-getDeployApprovalProcess--]]
==== `[.contract-item-name]#++getDeployApprovalProcess++#++() → struct ApprovalProcessResponse++` [.item-kind]#internal#

Gets the default deploy approval process configured for your deployment environment on OpenZeppelin Defender.

*Returns*

* (`struct ApprovalProcessResponse`) - Struct with the default deploy approval process ID and the associated address, such as a Relayer, EOA, or multisig wallet address.

[.contract-item]
[[Defender-Defender-getUpgradeApprovalProcess--]]
==== `[.contract-item-name]#++getUpgradeApprovalProcess++#++() → struct ApprovalProcessResponse++` [.item-kind]#internal#

Gets the default upgrade approval process configured for your deployment environment on OpenZeppelin Defender.
For example, this is useful for determining the default multisig wallet that you can use in your scripts to assign as the owner of your proxy.

*Returns*

* (`struct ApprovalProcessResponse`) - Struct with the default upgrade approval process ID and the associated address, such as a multisig or governor contract address.

