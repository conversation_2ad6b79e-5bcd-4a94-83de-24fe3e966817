name: checks

on:
  push:
    branches: [main]
  pull_request: {}
  workflow_dispatch: {}

env:
  FOUNDRY_PROFILE: ci

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Setup Node environment
      uses: ./.github/actions/setup

    - name: Run linter
      run: yarn lint

  docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Node environment
        uses: ./.github/actions/setup

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Check if docs are up to date
        run: yarn docgen:test

  tests:
    strategy:
      fail-fast: true

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Node environment
        uses: ./.github/actions/setup

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Run Forge version
        run: |
          forge --version
        id: version

      - name: Run tests
        run: |
          yarn test
        id: test
