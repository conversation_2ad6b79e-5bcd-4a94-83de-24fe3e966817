= Utilities

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/utils

Miscellaneous contracts and libraries containing utility functions you can use to improve security, work with new data types, or safely use low-level primitives.

 * {Math}, {SignedMath}: Implementation of various arithmetic functions.
 * {SafeCast}: Checked downcasting functions to avoid silent truncation.
 * {ECDSA}, {MessageHashUtils}: Libraries for interacting with ECDSA signatures.
 * {SignatureChecker}: A library helper to support regular ECDSA from EOAs as well as ERC-1271 signatures for smart contracts.
 * {MerkleProof}: Functions for verifying https://en.wikipedia.org/wiki/Merkle_tree[Merkle Tree] proofs.
 * {EIP712}: Contract with functions to allow processing signed typed structure data according to https://eips.ethereum.org/EIPS/eip-712[EIP-712].
 * {ReentrancyGuard}: A modifier that can prevent reentrancy during certain functions.
 * {Pausable}: A common emergency response mechanism that can pause functionality while a remediation is pending.
 * {Nonces}: Utility for tracking and verifying address nonces that only increment.
 * {ERC165, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}: Utilities for inspecting interfaces supported by contracts.
 * {BitMaps}: A simple library to manage boolean value mapped to a numerical index in an efficient way.
 * {EnumerableMap}: A type like Solidity's https://solidity.readthedocs.io/en/latest/types.html#mapping-types[`mapping`], but with key-value _enumeration_: this will let you know how many entries a mapping has, and iterate over them (which is not possible with `mapping`).
 * {EnumerableSet}: Like {EnumerableMap}, but for https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets]. Can be used to store privileged accounts, issued IDs, etc.
 * {DoubleEndedQueue}: An implementation of a https://en.wikipedia.org/wiki/Double-ended_queue[double ended queue] whose values can be removed added or remove from both sides. Useful for FIFO and LIFO structures.
 * {Checkpoints}: A data structure to store values mapped to an strictly increasing key. Can be used for storing and accessing values over time.
 * {Create2}: Wrapper around the https://blog.openzeppelin.com/getting-the-most-out-of-create2/[`CREATE2` EVM opcode] for safe use without having to deal with low-level assembly.
 * {Address}: Collection of functions for overloading Solidity's https://docs.soliditylang.org/en/latest/types.html#address[`address`] type.
 * {Arrays}: Collection of functions that operate on https://docs.soliditylang.org/en/latest/types.html#arrays[`arrays`].
 * {Base64}: On-chain base64 and base64URL encoding according to https://datatracker.ietf.org/doc/html/rfc4648[RFC-4648].
 * {Strings}: Common operations for strings formatting.
 * {ShortString}: Library to encode (and decode) short strings into (or from) a single bytes32 slot for optimizing costs. Short strings are limited to 31 characters.
 * {StorageSlot}: Methods for accessing specific storage slots formatted as common primitive types.
 * {Multicall}: Abstract contract with an utility to allow batching together multiple calls in a single transaction. Useful for allowing EOAs to perform multiple operations at once.
 * {Context}: An utility for abstracting the sender and calldata in the current execution context.

[NOTE]
====
Because Solidity does not support generic types, {EnumerableMap} and {EnumerableSet} are specialized to a limited number of key-value types.
====

== Math

{{Math}}

{{SignedMath}}

{{SafeCast}}

== Cryptography

{{ECDSA}}

{{MessageHashUtils}}

{{SignatureChecker}}

{{MerkleProof}}

{{EIP712}}

== Security

{{ReentrancyGuard}}

{{Pausable}}

{{Nonces}}

== Introspection

This set of interfaces and contracts deal with https://en.wikipedia.org/wiki/Type_introspection[type introspection] of contracts, that is, examining which functions can be called on them. This is usually referred to as a contract's _interface_.

Ethereum contracts have no native concept of an interface, so applications must usually simply trust they are not making an incorrect call. For trusted setups this is a non-issue, but often unknown and untrusted third-party addresses need to be interacted with. There may even not be any direct calls to them! (e.g. `ERC20` tokens may be sent to a contract that lacks a way to transfer them out of it, locking them forever). In these cases, a contract _declaring_ its interface can be very helpful in preventing errors.

{{IERC165}}

{{ERC165}}

{{ERC165Checker}}

== Data Structures

{{BitMaps}}

{{EnumerableMap}}

{{EnumerableSet}}

{{DoubleEndedQueue}}

{{Checkpoints}}

== Libraries

{{Create2}}

{{Address}}

{{Arrays}}

{{Base64}}

{{Strings}}

{{ShortStrings}}

{{StorageSlot}}

{{Multicall}}

{{Context}}
