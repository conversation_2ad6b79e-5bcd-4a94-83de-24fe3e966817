{"transactions": [{"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xf20a8950c368Ec48323092D6e4acF90aADf2BdC6", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000f20a8950c368ec48323092d6e4acf90aadf2bdc60000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x144", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x8E72a24221517E51502f20f387415a06b27A5b51", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe0000000000000000000000008e72a24221517e51502f20f387415a06b27a5b510000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x145", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x50d8Deadd2b3140B151CaB2C4FB76F1f59b236F8", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe00000000000000000000000050d8deadd2b3140b151cab2c4fb76f1f59b236f80000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x146", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x574582C44e3f1EF2cB29a7131B057FebBCC8244E", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000574582c44e3f1ef2cb29a7131b057febbcc8244e0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x147", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x281567fe62b587EC1755f6F33b80160F544Dc5d0", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000281567fe62b587ec1755f6f33b80160f544dc5d00000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x148", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x2705f6A8F01bd4A805D9FC73151DBe37BB8d1edE", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe0000000000000000000000002705f6a8f01bd4a805d9fc73151dbe37bb8d1ede0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x149", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xc9C9693b6A445D05Add0043662fad9Ac600Ad088", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000c9c9693b6a445d05add0043662fad9ac600ad0880000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x14a", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x7EfE40B2E6dA8b28AaB6Bd2D622B9Cd7f5fE077c", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe0000000000000000000000007efe40b2e6da8b28aab6bd2d622b9cd7f5fe077c0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x14b", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xa22DCB8F0A2848289124086F35ae9dB2a0006962", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x12dbf", "value": "0x0", "input": "0x44710fbe000000000000000000000000a22dcb8f0a2848289124086f35ae9db2a00069620000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x14c", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0ee0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x14d", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x40282d3Cf4890D9806BC1853e97a59C93D813653", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe00000000000000000000000040282d3cf4890d9806bc1853e97a59c93d8136530000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x14e", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xB5b901F1BB86421301138b5c45C1D3Fe96663161", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000b5b901f1bb86421301138b5c45c1d3fe966631610000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x14f", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xBAec8904499dcdee770c60df15b0C37EAC84Fb62", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000baec8904499dcdee770c60df15b0c37eac84fb620000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x150", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xfC4A23271b60887FC246B060B6931a08E2BC434c", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000fc4a23271b60887fc246b060b6931a08e2bc434c0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x151", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x65b142550aE82f4BB3792E1eEfb2FC35541A3837", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe00000000000000000000000065b142550ae82f4bb3792e1eefb2fc35541a38370000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x152", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x75149feEBb20E1fE5Ddb89302a6d4bACE70c14Ce", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe00000000000000000000000075149feebb20e1fe5ddb89302a6d4bace70c14ce0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x153", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x65B6D4770DAdcFba6d363dE86aA4D9c76283cea0", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe00000000000000000000000065b6d4770dadcfba6d363de86aa4d9c76283cea00000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x154", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xa6A9fdDC94BB4FE7520A2eA1CC2c433e18683342", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000a6a9fddc94bb4fe7520a2ea1cc2c433e186833420000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x155", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x18D04F05f80ADE5373849385a1c24E1E0a6d1744", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe00000000000000000000000018d04f05f80ade5373849385a1c24e1e0a6d17440000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x156", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xBD9C90D6774CB5320B54Bb7998b6Bcc5e4A9071f", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000bd9c90d6774cb5320b54bb7998b6bcc5e4a9071f0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x157", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x8f2eABa31B1b613ca78F2795bA05400F0583c5A4", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe0000000000000000000000008f2eaba31b1b613ca78f2795ba05400f0583c5a40000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x158", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x8f2eABa31B1b613ca78F2795bA05400F0583c5A4", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0xbc21", "value": "0x0", "input": "0x44710fbe0000000000000000000000008f2eaba31b1b613ca78f2795ba05400f0583c5a40000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x159", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x50d8Deadd2b3140B151CaB2C4FB76F1f59b236F8", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0xbc21", "value": "0x0", "input": "0x44710fbe00000000000000000000000050d8deadd2b3140b151cab2c4fb76f1f59b236f80000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x15a", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0x574582C44e3f1EF2cB29a7131B057FebBCC8244E", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0xbc21", "value": "0x0", "input": "0x44710fbe000000000000000000000000574582c44e3f1ef2cb29a7131b057febbcc8244e0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x15b", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "function": "setWhitelistedUser(address,bool)", "arguments": ["0xBd0Ce952bA069A1e15f3bf3916d4B07bBBdBC8B3", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xcb4d153604a6f21ff7625e5044e89c3b903599bc", "gas": "0x11d0b", "value": "0x0", "input": "0x44710fbe000000000000000000000000bd0ce952ba069a1e15f3bf3916d4b07bbbdbc8b30000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x15c", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [], "libraries": [], "pending": [], "returns": {}, "timestamp": 1747428879, "chain": 8453, "commit": "157d7bc"}