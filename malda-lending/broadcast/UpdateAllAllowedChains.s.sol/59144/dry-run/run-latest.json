{"transactions": [{"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x269c36a173d881720544fb303e681370158ff1fd", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x269c36a173d881720544fb303e681370158ff1fd", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x20c", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x20d", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x20e", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x2b588f7f4832561e46924f3ea54c244569724915", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x2b588f7f4832561e46924f3ea54c244569724915", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x20f", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x210", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x211", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x8bad0c523516262a439197736fff982f5e0987cc", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x8bad0c523516262a439197736fff982f5e0987cc", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x212", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "function": "updateAllowed<PERSON><PERSON><PERSON>(uint32,bool)", "arguments": ["1", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "gas": "0x12c6c", "value": "0x0", "input": "0x99c4383700000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x213", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [], "libraries": [], "pending": [], "returns": {}, "timestamp": 1747409808, "chain": 59144, "commit": "c569661"}