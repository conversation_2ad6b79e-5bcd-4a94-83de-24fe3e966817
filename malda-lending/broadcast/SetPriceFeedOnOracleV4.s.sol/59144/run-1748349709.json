{"transactions": [{"hash": "0xc3b07e39fa8ecb9b260cb77409245f0a7d7e2d5474de1ebfea8d04d9ffaf8c93", "transactionType": "CALL", "contractName": null, "contractAddress": "******************************************", "function": "setConfig(string,(address,address,string,uint256))", "arguments": ["mweETH", "(******************************************, ******************************************, USD, 18)"], "transaction": {"from": "******************************************", "to": "******************************************", "gas": "0x10f12", "value": "0x0", "input": "0xec2854060000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000066d776545544800000000000000000000000000000000000000000000000000000000000000000000000000006bd45e0f0adaae6481f2b4f3b867911bf5f8321b000000000000000000000000b71b0d0bf654d360e5cd5b39e8bbd7cee9970e090000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000035553440000000000000000000000000000000000000000000000000000000000", "nonce": "0x293", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0xb959", "logs": [{"address": "******************************************", "topics": ["0xe1c5d146221bf7b8aca8e1b646f7cb8c9a2f526541c9b73c3d16d19599ae914a"], "data": "0x0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000066d776545544800000000000000000000000000000000000000000000000000000000000000000000000000006bd45e0f0adaae6481f2b4f3b867911bf5f8321b000000000000000000000000b71b0d0bf654d360e5cd5b39e8bbd7cee9970e090000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000035553440000000000000000000000000000000000000000000000000000000000", "blockHash": "0x23bbc70033c7ae6b0d2992e39fe61eb4602a3b126f7cc3336e6be0354c25045d", "blockNumber": "0x12820a6", "transactionHash": "0xc3b07e39fa8ecb9b260cb77409245f0a7d7e2d5474de1ebfea8d04d9ffaf8c93", "transactionIndex": "0x0", "logIndex": "0x0", "removed": false}], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000100000000000000000000000000000000001000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000", "type": "0x2", "transactionHash": "0xc3b07e39fa8ecb9b260cb77409245f0a7d7e2d5474de1ebfea8d04d9ffaf8c93", "transactionIndex": "0x0", "blockHash": "0x23bbc70033c7ae6b0d2992e39fe61eb4602a3b126f7cc3336e6be0354c25045d", "blockNumber": "0x12820a6", "gasUsed": "0xb959", "effectiveGasPrice": "0x3e04fa4", "from": "******************************************", "to": "******************************************", "contractAddress": null}], "libraries": [], "pending": [], "returns": {}, "timestamp": 1748349709, "chain": 59144, "commit": "9b668a0"}