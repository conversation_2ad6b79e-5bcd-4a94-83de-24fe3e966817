{"transactions": [{"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "******************************************", "function": "setConfig(string,(address,address,string,uint256))", "arguments": ["mweETH", "(******************************************, ******************************************, USD, 18)"], "transaction": {"from": "******************************************", "to": "******************************************", "gas": "0x10f12", "value": "0x0", "input": "0xec2854060000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000066d776545544800000000000000000000000000000000000000000000000000000000000000000000000000006bd45e0f0adaae6481f2b4f3b867911bf5f8321b000000000000000000000000b71b0d0bf654d360e5cd5b39e8bbd7cee9970e090000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000035553440000000000000000000000000000000000000000000000000000000000", "nonce": "0x293", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [], "libraries": [], "pending": [], "returns": {}, "timestamp": 1748349696, "chain": 59144, "commit": "9b668a0"}