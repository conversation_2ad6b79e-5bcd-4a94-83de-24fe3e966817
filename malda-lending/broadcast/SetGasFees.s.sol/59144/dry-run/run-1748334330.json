{"transactions": [{"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x269c36a173d881720544fb303e681370158ff1fd", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x269c36a173d881720544fb303e681370158ff1fd", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x275", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x269c36a173d881720544fb303e681370158ff1fd", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x269c36a173d881720544fb303e681370158ff1fd", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x276", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x269c36a173d881720544fb303e681370158ff1fd", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x269c36a173d881720544fb303e681370158ff1fd", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x277", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x278", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x279", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xc7bc6bd45eb84d594f51ced3c5497e6812c7732f", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x27a", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x27b", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x27c", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0xdf0635c1ecfdf08146150691a97e2ff6a8aa1a90", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x27d", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x2b588f7f4832561e46924f3ea54c244569724915", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x2b588f7f4832561e46924f3ea54c244569724915", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x27e", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x2b588f7f4832561e46924f3ea54c244569724915", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x2b588f7f4832561e46924f3ea54c244569724915", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x27f", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x2b588f7f4832561e46924f3ea54c244569724915", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x2b588f7f4832561e46924f3ea54c244569724915", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x280", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x281", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x282", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1d8e8cefeb085f3211ab6a443ad9051b54d1cd1a", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x283", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x284", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x285", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x0b3c6645f4f2442ad4bbee2e2273a250461ca6f8", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x286", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x8bad0c523516262a439197736fff982f5e0987cc", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x8bad0c523516262a439197736fff982f5e0987cc", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x287", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x8bad0c523516262a439197736fff982f5e0987cc", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x8bad0c523516262a439197736fff982f5e0987cc", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x288", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x8bad0c523516262a439197736fff982f5e0987cc", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x8bad0c523516262a439197736fff982f5e0987cc", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x289", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "function": "setGasFee(uint32,uint256)", "arguments": ["8453", "83338358600000"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000210500000000000000000000000000000000000000000000000000004bcbb942b140", "nonce": "0x28a", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "function": "setGasFee(uint32,uint256)", "arguments": ["59144", "108904397956204"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c000000000000000000000000000000000000000000000000000000000000e7080000000000000000000000000000000000000000000000000000630c47f4646c", "nonce": "0x28b", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "function": "setGasFee(uint32,uint256)", "arguments": ["1", "3497531544510113"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x4df3dd62db219c47f6a7cb1be02c511afceadf5e", "gas": "0x11bbd", "value": "0x0", "input": "0xd8ddb33c0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000c6cfc854d1ea1", "nonce": "0x28c", "chainId": "0xe708"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [], "libraries": [], "pending": [], "returns": {}, "timestamp": 1748334330, "chain": 59144, "commit": "9b668a0"}