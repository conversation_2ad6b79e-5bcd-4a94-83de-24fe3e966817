{"transactions": [{"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xF8B6314e66EA3e4b62e229fa5F5F052058618404", "0x4b1cf52f49d174a2779824195ac83ffb0173882d2de2b8017a7a2ad31efa9bac", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a4613000000000000000000000000f8b6314e66ea3e4b62e229fa5f5f0520586184044b1cf52f49d174a2779824195ac83ffb0173882d2de2b8017a7a2ad31efa9bac0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x18f", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0x4b1cf52f49d174a2779824195ac83ffb0173882d2de2b8017a7a2ad31efa9bac", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827d4b1cf52f49d174a2779824195ac83ffb0173882d2de2b8017a7a2ad31efa9bac0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x190", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0xaeffe8a027d13ff1f9fbecaa8f7fc0ff2c5c5d71561a1a572f6bafeeba68abfd", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827daeffe8a027d13ff1f9fbecaa8f7fc0ff2c5c5d71561a1a572f6bafeeba68abfd0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x191", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0xc8e17e1f507bc6ac21ffd444ba9d6c9d6ea2defdac21571990ea0eb8bcde3a24", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827dc8e17e1f507bc6ac21ffd444ba9d6c9d6ea2defdac21571990ea0eb8bcde3a240000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x192", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0xcf3889b3d647b5db8c87e0e05aca2acdd693a72c71726764f2198ee27a21c728", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827dcf3889b3d647b5db8c87e0e05aca2acdd693a72c71726764f2198ee27a21c7280000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x193", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0x79bb2eff4b96ce24cc809a1642a7365c4980ebeb15be2580a619e03726e41a98", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827d79bb2eff4b96ce24cc809a1642a7365c4980ebeb15be2580a619e03726e41a980000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x194", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0x3e008573b76fa95febaab72c06ec152363f92f2b27dcaf00fbd0bb77a70593db", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107c3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827d3e008573b76fa95febaab72c06ec152363f92f2b27dcaf00fbd0bb77a70593db0000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x195", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0x46c2ab528350fb4f59e61eb6c0382446a3636f13f7846680a2734e5f829735a4", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827d46c2ab528350fb4f59e61eb6c0382446a3636f13f7846680a2734e5f829735a40000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x196", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0xb0487bdd3330b4d082e49dfc24e1aa23706abcc632f9215fee20a474a9a88457", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827db0487bdd3330b4d082e49dfc24e1aa23706abcc632f9215fee20a474a9a884570000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x197", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0xeefbfa32ce9dd99a431be27221a8732bc1e7b3d032ff4cbd56226b17783f7413", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827deefbfa32ce9dd99a431be27221a8732bc1e7b3d032ff4cbd56226b17783f74130000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x198", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0x91B945CbB063648C44271868a7A0c7BdFf64827D", "0xaa85d4d7655b58b3177edbbb41bc17f2e2ebffd26ace9d6b56244a5ee16c7806", "true"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x107e3", "value": "0x0", "input": "0xec3a461300000000000000000000000091b945cbb063648c44271868a7a0c7bdff64827daa85d4d7655b58b3177edbbb41bc17f2e2ebffd26ace9d6b56244a5ee16c78060000000000000000000000000000000000000000000000000000000000000001", "nonce": "0x199", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0x4b1cf52f49d174a2779824195ac83ffb0173882d2de2b8017a7a2ad31efa9bac", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x91aa", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0ee4b1cf52f49d174a2779824195ac83ffb0173882d2de2b8017a7a2ad31efa9bac0000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x19a", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0xaeffe8a027d13ff1f9fbecaa8f7fc0ff2c5c5d71561a1a572f6bafeeba68abfd", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x91aa", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0eeaeffe8a027d13ff1f9fbecaa8f7fc0ff2c5c5d71561a1a572f6bafeeba68abfd0000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x19b", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0xc8e17e1f507bc6ac21ffd444ba9d6c9d6ea2defdac21571990ea0eb8bcde3a24", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x91aa", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0eec8e17e1f507bc6ac21ffd444ba9d6c9d6ea2defdac21571990ea0eb8bcde3a240000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x19c", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0x3e008573b76fa95febaab72c06ec152363f92f2b27dcaf00fbd0bb77a70593db", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x9188", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0ee3e008573b76fa95febaab72c06ec152363f92f2b27dcaf00fbd0bb77a70593db0000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x19d", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0x46c2ab528350fb4f59e61eb6c0382446a3636f13f7846680a2734e5f829735a4", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x91aa", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0ee46c2ab528350fb4f59e61eb6c0382446a3636f13f7846680a2734e5f829735a40000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x19e", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0xb0487bdd3330b4d082e49dfc24e1aa23706abcc632f9215fee20a474a9a88457", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x91aa", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0eeb0487bdd3330b4d082e49dfc24e1aa23706abcc632f9215fee20a474a9a884570000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x19f", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0xeefbfa32ce9dd99a431be27221a8732bc1e7b3d032ff4cbd56226b17783f7413", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x91aa", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0eeeefbfa32ce9dd99a431be27221a8732bc1e7b3d032ff4cbd56226b17783f74130000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x1a0", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": null, "transactionType": "CALL", "contractName": null, "contractAddress": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "function": "allowFor(address,bytes32,bool)", "arguments": ["0xB819A871d20913839c37f316Dc914b0570bfc0eE", "0xaa85d4d7655b58b3177edbbb41bc17f2e2ebffd26ace9d6b56244a5ee16c7806", "false"], "transaction": {"from": "0xb819a871d20913839c37f316dc914b0570bfc0ee", "to": "0x1211d07f0ebea8994f23ec26e1e512929fc8ab08", "gas": "0x91aa", "value": "0x0", "input": "0xec3a4613000000000000000000000000b819a871d20913839c37f316dc914b0570bfc0eeaa85d4d7655b58b3177edbbb41bc17f2e2ebffd26ace9d6b56244a5ee16c78060000000000000000000000000000000000000000000000000000000000000000", "nonce": "0x1a1", "chainId": "0x2105"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [], "libraries": [], "pending": [], "returns": {}, "timestamp": 1748271362, "chain": 8453, "commit": "9b668a0"}