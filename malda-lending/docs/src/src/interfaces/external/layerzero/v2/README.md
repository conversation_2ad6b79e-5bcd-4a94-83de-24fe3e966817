

# Contents
- [MessagingParams](ILayerZeroEndpointV2.sol\struct.MessagingParams.md)
- [MessagingReceipt](ILayerZeroEndpointV2.sol\struct.MessagingReceipt.md)
- [MessagingFee](ILayerZeroEndpointV2.sol\struct.MessagingFee.md)
- [Origin](ILayerZeroEndpointV2.sol\struct.Origin.md)
- [ILayerZeroEndpointV2](ILayerZeroEndpointV2.sol\interface.ILayerZeroEndpointV2.md)
- [SendParam](ILayerZeroOFT.sol\struct.SendParam.md)
- [OFTLimit](ILayerZeroOFT.sol\struct.OFTLimit.md)
- [OFTReceipt](ILayerZeroOFT.sol\struct.OFTReceipt.md)
- [OFTFeeDetail](ILayerZeroOFT.sol\struct.OFTFeeDetail.md)
- [ILayerZeroOFT](ILayerZeroOFT.sol\interface.ILayerZeroOFT.md)
- [ILayerZeroReceiverV2](ILayerZeroReceiverV2.sol\interface.ILayerZeroReceiverV2.md)
- [MessageLibType](IMessageLib.sol\enum.MessageLibType.md)
- [IMessageLib](IMessageLib.sol\interface.IMessageLib.md)
- [SetConfigParam](IMessageLibManager.sol\struct.SetConfigParam.md)
- [IMessageLibManager](IMessageLibManager.sol\interface.IMessageLibManager.md)
- [IMessagingChannel](IMessagingChannel.sol\interface.IMessagingChannel.md)
- [IMessagingComposer](IMessagingComposer.sol\interface.IMessagingComposer.md)
- [IMessagingContext](IMessagingContext.sol\interface.IMessagingContext.md)
