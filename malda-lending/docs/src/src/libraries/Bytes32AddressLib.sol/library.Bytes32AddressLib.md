# Bytes32AddressLib
[Git Source](https://github.com/malda-protocol/malda-lending/blob/ae9b756ce0322e339daafd68cf97592f5de2033d/src\libraries\Bytes32AddressLib.sol)

**Author:**
Solmate (https://github.com/transmissions11/solmate/blob/main/src/utils/Bytes32AddressLib.sol)

Library for converting between addresses and bytes32 values.


## Functions
### fromLast20Bytes


```solidity
function fromLast20Bytes(bytes32 bytesValue) internal pure returns (address);
```

### fillLast12Bytes


```solidity
function fillLast12Bytes(address addressValue) internal pure returns (bytes32);
```

