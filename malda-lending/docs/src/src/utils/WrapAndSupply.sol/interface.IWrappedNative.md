# IWrappedNative
[Git Source](https://github.com/malda-protocol/malda-lending/blob/ae9b756ce0322e339daafd68cf97592f5de2033d/src\utils\WrapAndSupply.sol)


## Functions
### deposit


```solidity
function deposit() external payable;
```

### transfer


```solidity
function transfer(address to, uint256 value) external returns (bool);
```

### withdraw


```solidity
function withdraw(uint256) external;
```

