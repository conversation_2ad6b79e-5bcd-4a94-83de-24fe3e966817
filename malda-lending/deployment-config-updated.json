{"networks": {"linea_sepolia": {"chainId": 59141, "isHost": true, "deployer": {"owner": "******************************************", "salt": "FirstTry"}, "oracle": {"oracleType": "MixedPriceOracleV3", "stalenessPeriod": 86400, "usdcFeed": "******************************************", "wethFeed": "******************************************"}, "markets": [{"borrowCap": 1000000000000, "borrowRateMaxMantissa": 500000000000000000, "collateralFactor": 800000000000000000, "decimals": 6, "interestModel": {"baseRate": 20000000000000000, "blocksPerYear": 2102400, "jumpMultiplier": 500000000000000000, "kink": 800000000000000000, "multiplier": 100000000000000000, "name": "Mock USDC Interest Model"}, "name": "mUSDCMock", "priceFeed": "******************************************", "supplyCap": 1000000000000, "symbol": "mUSDCMock", "underlying": "******************************************"}, {"borrowCap": 1000000000000000000000, "borrowRateMaxMantissa": 500000000000000000, "collateralFactor": 750000000000000000, "decimals": 18, "interestModel": {"baseRate": 10000000000000000, "blocksPerYear": 2102400, "jumpMultiplier": 400000000000000000, "kink": 900000000000000000, "multiplier": 80000000000000000, "name": "Mock wstETH Interest Model"}, "name": "mwstETHMock", "priceFeed": "******************************************", "supplyCap": 1000000000000000000000, "symbol": "mwstETHMock", "underlying": "******************************************"}], "roles": [], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "******************************************"}, "allowedChains": [11155420, 11155111]}, "op_sepolia": {"chainId": 11155420, "isHost": false, "deployer": {"owner": "******************************************", "salt": "FirstTry"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDCMock", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mUSDCMock", "underlying": "******************************************"}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mwstETHMock", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mwstETHMock", "underlying": "******************************************"}], "roles": [], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "******************************************"}}, "sepolia": {"chainId": 11155111, "isHost": false, "deployer": {"owner": "******************************************", "salt": "FirstTry"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mUSDCMock", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mUSDCMock", "underlying": "******************************************"}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": ""}, "name": "mwstETHMock", "priceFeed": "******************************************", "supplyCap": 0, "symbol": "mwstETHMock", "underlying": "******************************************"}], "roles": [], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "******************************************"}}}}