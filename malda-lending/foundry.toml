[profile.default]
  libs = ['node_modules', 'lib']
  out = "out"
  src = "src"
  ffi = true

  evm_version = 'london'
  optimizer = true
  optimizer_runs = 200
  solc_version = '0.8.28'

  remappings = [
    "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/",
    "@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/",
    "risc0/=lib/risc0-ethereum/contracts/src/",
    "forge-std/=lib/forge-std/src/"
  ]

  # forge-deploy
  fs_permissions = [
    { access = "read-write", path = "./deployments" },
    { access = "read", path = "./out" },
    { access = "read", path = "./deployment-config.json" },
    { access = "read", path = "./deployment-config-updated.json" },
    { access = "read", path = "./deployment-config-release.json" },
    { access = "read", path = "./deployment-config-testnet.json" },
    { access = "read", path = "./deployment-config.sepolia.json" },
    { access = "read", path = "./deployment-config.op.json" },
    { access = "read", path = "./deployment-config.linea.json" },
    { access = "read", path = "./deployment-rebalancer-config.json" },
    { access = "read-write", path = "./test/" },
    { access = "read-write", path = "./script/deployment/mainnet/output/" }
]

[rpc_endpoints]
#mainnet
linea="${LINEA_RPC_URL}"
mainnet="${MAINNET_RPC_URL}"
optimism="${OPTIMISM_RPC_URL}"
base="${BASE_RPC_URL}"
#testnet
linea_sepolia = "${LINEA_SEPOLIA_RPC_URL}"
op_sepolia = "${OP_SEPOLIA_RPC_URL}"
sepolia = "${SEPOLIA_RPC_URL}"
arbitrum_sepolia = "${ARBITRUM_SEPOLIA_RPC_URL}"
base_sepolia = "${BASE_SEPOLIA_RPC_URL}"
anvil1 = "http://localhost:8545"
anvil2 = "http://localhost:8546"

[etherscan]
#mainnet
linea = {chain = "59144", url = "https://api.lineascan.build/api", key = "${LINEA_ETHERSCAN_API_KEY}"}
mainnet = {chain = "1", url = "https://api.etherscan.io/api", key = "${MAINNET_ETHERSCAN_API_KEY}"}
optimism = {chain = "10", url = "https://api-optimistic.etherscan.io/api", key = "${OPTIMISM_ETHERSCAN_API_KEY}"}
base = {chain = "8453", url = "https://api.basescan.org/api", key = "${BASE_ETHERSCAN_API_KEY}"}
#testnet
linea_sepolia = {chain = "59141", url = "https://api-sepolia.lineascan.build/api", key = "${LINEA_SEPOLIA_ETHERSCAN_API_KEY}"}
op_sepolia = {chain = "11155420", url = "https://api-sepolia-optimistic.etherscan.io/api", key = "${OP_SEPOLIA_ETHERSCAN_API_KEY}"}
sepolia = {chain = "11155111", url = "https://api-sepolia.etherscan.io/api", key = "${SEPOLIA_ETHERSCAN_API_KEY}"}
arbitrum_sepolia = {url = "https://api-sepolia-arbitrum.etherscan.io/api", key = "${ARBITRUM_SEPOLIA_ETHERSCAN_API_KEY}"}
base_sepolia = {url = "https://api-sepolia-base.etherscan.io/api", key = "${BASE_SEPOLIA_ETHERSCAN_API_KEY}"}

[fuzz]
runs = 10