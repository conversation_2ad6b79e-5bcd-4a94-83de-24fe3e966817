{"generic": {"version": 1, "connextDomains": [{"chainId": 1, "domainId": 6648936}, {"chainId": 42161, "domainId": 1634886255}, {"chainId": 10, "domainId": 10}, {"chainId": 8453, "domainId": 1650553709}]}, "networks": {"linea_sepolia": {"ownership": "******************************************", "chainId": 59141, "isHost": true, "deployer": {"owner": "******************************************", "salt": "DeployerV1.1"}, "oracle": {"oracleType": "MixedPriceOracleV3", "stalenessPeriod": 864000}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": **************, "collateralFactor": 900000000000000000, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": ********, "jumpMultiplier": 3499999999994448000, "kink": 920000000000000000, "multiplier": 50605736204435511, "name": "mUSDCMock Interest Model"}, "name": "mUSDCMock", "supplyCap": 0, "symbol": "mUSDCMock", "underlying": "******************************************"}, {"borrowCap": 0, "borrowRateMaxMantissa": **************, "collateralFactor": 810000000000000000, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": ********, "jumpMultiplier": 8499924722164496000, "kink": 800000000000000000, "multiplier": *****************, "name": "mwstETHMock Interest Model"}, "name": "mwstETHMock", "supplyCap": 0, "symbol": "mwstETHMock", "underlying": "******************************************"}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0x27983ee173aD10E171D17C9c5C14d5baFE997609"}, "allowedChains": [********, ********]}, "op_sepolia": {"ownership": "******************************************", "chainId": ********, "isHost": false, "deployer": {"owner": "******************************************", "salt": "DeployerV1.1"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mUSDCMock Interest Model"}, "name": "mUSDCMock", "supplyCap": 0, "symbol": "mUSDCMock", "underlying": "******************************************"}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mwstETHMock Interest Model"}, "name": "mwstETHMock", "supplyCap": 0, "symbol": "mwstETHMock", "underlying": "******************************************"}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0xB369b4dd27FBfb59921d3A4a3D23AC2fc32FB908"}}, "sepolia": {"ownership": "******************************************", "chainId": ********, "isHost": false, "deployer": {"owner": "******************************************", "salt": "DeployerV1.1"}, "markets": [{"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 6, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mUSDCMock Interest Model"}, "name": "mUSDCMock", "supplyCap": 0, "symbol": "mUSDCMock", "underlying": "******************************************"}, {"borrowCap": 0, "borrowRateMaxMantissa": 0, "collateralFactor": 0, "decimals": 18, "interestModel": {"baseRate": 0, "blocksPerYear": 0, "jumpMultiplier": 0, "kink": 0, "multiplier": 0, "name": "mwstETHMock Interest Model"}, "name": "mwstETHMock", "supplyCap": 1000000000000000000000, "symbol": "mwstETHMock", "underlying": "******************************************"}], "roles": [{"accounts": ["******************************************"], "roleName": "CHAINS_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PAUSE_MANAGER"}, {"accounts": ["******************************************"], "roleName": "PROOF_FORWARDER"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_RESERVE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_PAUSE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BORROW_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_SUPPLY_CAP"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_ORACLE"}, {"accounts": ["******************************************"], "roleName": "GUARDIAN_BRIDGE"}, {"accounts": ["******************************************"], "roleName": "REBALANCER_EOA"}], "zkVerifier": {"imageId": "0xd1333b14cc29a5d8828e9f5842a44c881efbabec67faf033633816939bde3c08", "verifierAddress": "0x925d8331ddc0a1F0d96E68CF073DFE1d92b69187"}}}}