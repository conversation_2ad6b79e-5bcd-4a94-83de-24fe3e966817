# Licensing Information for malda-lending

## Primary License: Business Source License 1.1 (BSL 1.1)

This repository, malda-lending, developed by **Merge Layers Inc.**, is licensed under the **Business Source License 1.1 (BSL 1.1)**.

* **Licensor:** Merge Layers Inc.
* **Change License:** Apache License 2.0
* **Change Date:** Two years from the date of each software version's first public release.
* **Additional Use Grant:** Use of the Licensed Work is permitted solely for non-production purposes, including development, testing, and evaluation. Any use in a production environment requires a separate commercial license from Merge Layers Inc.

You can find the full text of the BSL 1.1 as applied to this project in the `LICENSE-BSL` file. Under the BSL, you have access to the source code and can use it according to its terms, but production use is generally restricted until the Change Date unless you have a commercial license or fall under the Additional Use Grant.

## Acknowledgment of Base Licenses & Derivative Work Status

**IMPORTANT:** This project is a derivative work. It builds upon, modifies, and extends code from:

* **Compound V2**, which is licensed under the **BSD 3-Clause "New" or "Revised" License**. A copy of this license is available in the `LICENSE-COMPOUND-V2` file.

* **Risc0**, which is licensed under the **Apache License 2.0**. A copy of this license is available in the `LICENSE-RISC0` file.

While Merge Layers Inc. licenses this repository *as a whole* under BSL 1.1, by using this software, **you MUST also comply with the terms, conditions, and notice requirements of the underlying Apache License 2.0**. This license contains specific terms regarding distribution, modification notices, patent grants, and requires the inclusion of attribution notices, which apply to the original components, even as modified within this project.

While Merge Layers Inc. licenses this repository *as a whole* under BSL 1.1, by using this software, **you MUST also comply with the terms, conditions, and notice requirements of the underlying BSD 3-Clause License**. This license contains terms regarding redistribution and attribution that apply to the original components, even as modified within this project.

Required attributions can be found in the `NOTICE` file (if applicable) and within the original source code components.

## User Responsibility & Risk Acknowledgment

This "blanket" licensing approach aims for simplicity but places a **higher burden on you, the user**, to understand the interplay between the BSL 1.1 and the underlying BSD 3-Clause license. You are responsible for ensuring your use case complies with **all** applicable license terms. Merge Layers Inc. provides this software as-is, and this licensing structure involves complexities. **Consult with legal counsel if you are unsure about your rights or obligations.**

## Summary

* **Your Code & Modifications:** BSL 1.1 (view `LICENSE-BSL`).
* **Base Code (Compound V2):** BSD 3-Clause (view `LICENSE-COMPOUND-V2`).
* **Your Obligation:** Comply with BSL *and* the BSD 3-Clause license.

For commercial licensing inquiries regarding production use before the Change Date, <NAME_EMAIL>.

---
Merge Layers Inc.
