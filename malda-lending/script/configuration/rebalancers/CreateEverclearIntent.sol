// SPDX-License-Identifier: UNLICENSED
pragma solidity =0.8.28;

import {IERC20} from "lib/forge-std/src/interfaces/IERC20.sol";
import {Script} from "forge-std/Script.sol";
import {IEverclearSpoke} from "src/interfaces/external/everclear/IEverclearSpoke.sol";

contract CreateEverclearIntent is Script {
    function run() public virtual {
        uint256 key = vm.envUint("PRIVATE_KEY");

        address everclearSpoke = ******************************************; //op sep
        bytes memory data = "";

        address market = ******************************************; //destination
        address outputAsset = ******************************************; //weth on eth sep
        uint256 amount = 1e16;
        address token = ******************************************; //weth on op sep

        uint32[] memory destinations = new uint32[](1);
        destinations[0] = uint32(11155111); //eth sep

        vm.startBroadcast(key);
        IERC20(token).approve(everclearSpoke, amount);
        IEverclearSpoke(everclearSpoke).newIntent(destinations, market, token, outputAsset, amount, 0, 0, data);
        vm.stopBroadcast();
    }
}
