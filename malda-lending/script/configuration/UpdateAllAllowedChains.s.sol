// SPDX-License-Identifier: UNLICENSED
pragma solidity =0.8.28;

import {mErc20Host} from "../../src/mToken/host/mErc20Host.sol";
import {Script, console} from "forge-std/Script.sol";

contract UpdateAllAllowedChains is Script {
    function run() public virtual {
        uint256 key = vm.envUint("PRIVATE_KEY");

        bool isAllowed = true;

        address[] memory lineaAndEthMarkets = new address[](8);
        lineaAndEthMarkets[0] = ******************************************;
        lineaAndEthMarkets[1] = ******************************************;
        lineaAndEthMarkets[2] = ******************************************;
        lineaAndEthMarkets[3] = ******************************************;
        lineaAndEthMarkets[4] = ******************************************;
        lineaAndEthMarkets[5] = ******************************************;
        lineaAndEthMarkets[6] = ******************************************;
        lineaAndEthMarkets[7] = ******************************************;

        uint32[] memory chains = new uint32[](2);
        chains[0] = 1;
        //chains[1] = 59144;
        chains[1] = 8453;

        for (uint256 i; i < lineaAndEthMarkets.length; i++) {
            address market = lineaAndEthMarkets[i];
            for (uint256 j; j < chains.length; j++) {
                uint32 chainId = chains[j];
                if (mErc20Host(market).allowedChains(chainId) == isAllowed) {
                    console.log("Allowed chain already set");
                    continue;
                }

                vm.startBroadcast(key);
                mErc20Host(market).updateAllowedChain(chainId, isAllowed);
                vm.stopBroadcast();
                console.log("Allowed chain updated for market %s", market);
            }
        }
    }
}
