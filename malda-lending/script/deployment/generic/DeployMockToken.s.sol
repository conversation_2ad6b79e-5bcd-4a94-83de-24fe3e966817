// SPDX-License-Identifier: UNLICENSED
pragma solidity =0.8.28;

import {Script, console} from "forge-std/Script.sol";
import {Deployer} from "src/utils/Deployer.sol";
import {ERC20Mock} from "test/mocks/ERC20Mock.sol";

/**
 * forge script DeployMockToken  \
 *     --slow \
 *     --verify \
 *     --verifier-url <url> \
 *     --rpc-url <url> \
 *     --etherscan-api-key <key> \
 *     --broadcast
 */
contract DeployMockToken is Script {
    string constant NAME = "wstETH Mock";
    string constant SYMBOL = "wstETH-M";
    uint8 constant DECIMALS = 18;
    address constant OWNER = ******************************************;
    address constant POH_VERIFY = ******************************************; //linea
    uint256 constant LIMIT = 1000e6;

    function run(Deployer deployer) public returns (address) {
        bytes32 salt = keccak256(abi.encodePacked(msg.sender, bytes(vm.envString("DEPLOY_SALT")), bytes("Mock-wstETH")));

        uint256 key = vm.envUint("PRIVATE_KEY");
        vm.startBroadcast(key);

        address created = deployer.create(
            salt,
            abi.encodePacked(type(ERC20Mock).creationCode, abi.encode(NAME, SYMBOL, DECIMALS, OWNER, POH_VERIFY, LIMIT))
        );

        console.log(" ERC20Mock deployed at: %s", created);

        vm.stopBroadcast();

        return created;
    }
}
