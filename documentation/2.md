# Architecture

<figure><img src="https://*********-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FGI1QYP4cTEZrQ1wnsNvu%2Fuploads%2FWyS5Le3IoC5ZbJvd8PAy%2Fachitecture_1.png?alt=media&#x26;token=62f4a82d-1ee4-4384-ad95-c4b676b5d272" alt=""><figcaption><p>Malda Host Chain and Extension Chain Connection</p></figcaption></figure>

Malda introduces a Host Chain and Extension Chain architecture.

Linea as the host chain stores the full accounting and is the final ledger of balances within the Malda protocol. Being a zk-rollup it is not subject to reorgs from fraud proofs, with this zk-finality, Malda protocol can have a consistent state even with integrating optimistic rollups.

Extension Chains do not contain protocol logic, but operate with zk-proofs. Deposit transactions emit a zk-proof to Linea via the Sequencer, while withdraw transactions require a valid zk-proof generated on the Malda state stored on Linea.

The Malda Sequencer receives each order, generates a valid proof on the transaction and delivers it to the destination (Host or Extension)

The last key piece for proof verification is the Risc0 Verifier contract deployed on all supported chains. This is deployed and secured by the RiscZero team and used by Malda contracts to verify the validity of our proofs.

<figure><img src="https://*********-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FGI1QYP4cTEZrQ1wnsNvu%2Fuploads%2FpEq1KcCvWHGAOy7JIA9D%2Farctitecture_2.png?alt=media&#x26;token=69d161bb-c353-47da-bd84-a1d1b94c11d1" alt=""><figcaption><p>Malda Sequencer</p></figcaption></figure>

### Self-Sequencing

Users have the ability to self-sequence the transactions for themselves, in case of Sequencer failure or if they wish to do it for themselves.

Transactions requiring a proof (e.g. Extension chain transactions) can flag this in the `lineaSelector` field, and the Malda sequencer will ignore the transaction.

The user needs to generate the proof for themselves in this case by one of the following method:

* Acquire an API key for Risc0 Bonsai service and request a proof
  * A detailed guide will be released with mainnet launch
* Submit the request to a prover network such as Boundless (expected to go live in 2025)
* Generate the proof on local machine. We have observed that a proof can be generated on a home computer as well, we are going to release guides and benchmarks with mainnet launch

Once they have received the proof they need to call the corresponding function on either the host or the external chain.

We have a more detailed guide in the developer documentation on this.

Note: The most reliable and simple way for users to interact with Malda is to rely on the sequencer. The sequencer does not have the ability to submit false transactions, as each proof contains the required information for the user.



