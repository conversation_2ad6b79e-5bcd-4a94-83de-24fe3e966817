# Global Interest Rates

Interest rates in Malda are calculated based on the global supply and global borrow of each market.

We are using the following formula

$$
R_{\text{supply}} = \left(\left(1 + R_{\text{borrow}}\right)^{\frac{1}{\text{Blocks Per Year}}} - 1\right) \times \text{Total Borrowed} \times \left(1 - R_{\text{reserve}}\right) \div \text{Total Supply}
$$

$$
R_{\text{supply}}^{\text{(APY)}} = \left(1 + R_{\text{supply}}\right)^{\text{Blocks Per Year}} - 1
$$

Thanks to the unified global pool design users enjoy the same interest rates across the wider Ethereum ecosystem, providing an excellent opportunity for lenders and borrowers to access better interest rates without navigating between the different L2s of Ethereum, with Malda being positioned as natural arbitrage capturer.
