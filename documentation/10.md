# Introduction

The following pages contains the developer documentation for the Malda protocol.&#x20;

Malda consists of 3 components:

* Malda ZK Coprocessor
  * zkVM implementation of the offchain application logic
* Malda Lending
  * Solidity implementation of the onchain application logic
* Malda Sequencer (not yet open-sourced)
  * The first application specific multichain sequencer, which connects the onchain and offchain components&#x20;

The documentation is split into the above mentioned parts and explain the core functionality for technical developers.&#x20;



## About Malda Protocol

Malda Protocol solves the fragmentation problem in DeFi by creating a unified lending experience across multiple EVM networks. The protocol enables users to:

* Access lending markets across different L2s as if they were a single network
* Unified liquidity and interest rates across all chains
* Execute lending operations across chains without bridging or wrapping assets
* Maintain full control of their funds in a self-custodial manner



## About the ZK Coprocessor

This repository contains the ZK coprocessor, a critical component that enables Malda's cross-chain capabilities through zero-knowledge proofs. The coprocessor:

* Generates zero-knowledge proofs that verify cross-chain state and operations
* Enables trustless cross-chain communication without relying on bridges or oracles
* Provides cryptographic guarantees for cross-chain lending operations
