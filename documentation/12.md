# Technical Details

#### Proof Generation and Verification



The ZK coprocessor generates and verifies proofs through several key components:

**Proof Data Generation**



The `get_proof_data` functionality is central to the protocol's cross-chain operations:

1. **Guest Program**
   * Executes inside the RISC Zero zkVM
   * Verifies cross-chain state through zero-knowledge proofs
   * Validates user positions and market states across chains
   * Generates cryptographic proofs of state verification
   * Handles view calls to market contracts for state verification



**Chain-Specific Verification**

​Each supported chain has specialized verification mechanisms:

1. **Ethereum (L1)**
   * Light client verification through beacon chain
   * Proof state via OPstack L1 reads
2. **Optimism/Base (OpStack)**
   * Sequencer commitment verification
   * Dispute game validation
   * L1 block inclusion proofs
3. **Linea**
   * Sequencer commitment verification
   * L1 block inclusion proofs



**Self-Sequencing**

​While the [Sequencer Infrastructure](https://github.com/malda-protocol/malda-zk-coprocessor/tree/minimalDocs#sequencer-infrastructure) handles proof generation and submission for most users, the protocol maintains censorship resistance through self-sequencing capabilities. Users can generate and submit their own proofs if:

* The Sequencer is unavailable
* The Sequencer attempts to censor transactions
* Users prefer to handle their own proof generation
* Additional security guarantees are required



**Self-Sequencing Guide**​To generate proofs independently:

1. **Setup**&#x20;

```
# Install RISC Zero zkVM and Bonsai SDK
For detailed installation instructions, see the [RISC Zero documentation](https://dev.risczero.com/api/zkvm/install).
```

2. **Environment Configuration** Create a `.env` file with required RPC endpoints:

```
RPC_URL_LINEA=
RPC_URL_ETHEREUM=
RPC_URL_BASE=
RPC_URL_OPTIMISM=
RPC_URL_BEACON=https://www.lightclientdata.org
# ... other chain configurations
```

3. **Proof Generation** Use the Malda SDK to generate proofs:

```
// Using Bonsai SDK for remote proving
pub async fn get_proof_data_prove_sdk(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
) -> Result<MaldaProveInfo, Error>

// Using local zkVM for proving
pub async fn get_proof_data_prove(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<Vec<u64>>,
    l1_inclusion: bool,
) -> Result<MaldaProveInfo, Error>
```

4. **Transaction Preparation** Extract the required data for on-chain submission:

```
let journal = Bytes::from(proof_info.receipt.journal.bytes);
let seal = risc0_ethereum_contracts::encode_seal(&receipt);
```

Note: For self-sequencing, `l1_inclusion` must be set to `true` to ensure additional security guarantees against potential reorg exploits.
