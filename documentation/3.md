# End-user lifecycle

There are 3 types of transactions within Malda, under the hood they each have a different lifecycle:

**1. Linea native transactions**

These transactions go through the same way as in a legacy lending protocol.

<figure><img src="https://799135553-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FGI1QYP4cTEZrQ1wnsNvu%2Fuploads%2FmJpOBSDRBxxRTFd5NSsK%2Fmalda_external-doc_03_End-user_Lifecycle_01_Linea_Native_v2.png?alt=media&#x26;token=4d6b5fdd-8a9a-4caf-97a1-14fbec25afd9" alt=""><figcaption></figcaption></figure>

**2. Deposit type transaction from Extension**

We define Supply and Repay as deposit type of transactions, as both of them have the same transaction flow from an architectural point of view. These are both depositing funds to the extension pool contract and we verify those transactions on the host chain to append the ledger.

<figure><img src="https://799135553-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FGI1QYP4cTEZrQ1wnsNvu%2Fuploads%2Fl3960z1M9K5dc1iRb94I%2Fsupply_repay_extension_chains.png?alt=media&#x26;token=8f4e8ff5-edbc-40dc-9fb6-d6e63b5c38b5" alt=""><figcaption></figcaption></figure>

**3. Withdraw type of transactions on Extension**

Borrowing and withdrawing are defined as withdraw type of transactions. These are both requesting a withdrawal from the extension pools on Linea, and verify that withdrawals are valid on the extension chain via the generated proof.

<figure><img src="https://799135553-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FGI1QYP4cTEZrQ1wnsNvu%2Fuploads%2FKYVdD5hh5Uwwt4VCdVdi%2Fborrow_withdraw_extension_chains.png?alt=media&#x26;token=ae2624a7-5f39-48a2-88c8-44b9367ec495" alt=""><figcaption></figcaption></figure>
