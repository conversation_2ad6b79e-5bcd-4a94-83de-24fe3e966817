# Rebalancing

The rebalancing module continuously monitors borrow activity on Malda and other money markets, chains’ inflows and outflows, and additional metrics to forecast future borrow demand per chain to rebalance token balances across chains.

The expected borrow demand for a token on a chain is forecasted based on a linear regression of the borrow demand over the last 7 days. If forecasts are shown to be inaccurate, other formulas will be used to minimize the observed delta.

Additional signals include deviations in general borrow demand on other money markets or outliers of chain inflows/outflows.

Based on that, each chain is assigned a weight defined as:

$$
\text{expected chain weight}_i = \frac{\text{expected borrow demand}_i}{\sum \text{expected borrow demand}_i}
$$

The available liquidity is then allocated according to the calculated chain weights. A rebalancing is triggered when:

$$
\frac{\text{expected chain weight}_i}{\text{current chain weight}_i} > 1.2
$$

This acts as an initial benchmark which will be adjusted in the future based on real-world data, optimizing for profitability. The liquidity is pulled from chains with expected accessible liquidity. The system moves liquidity in a way that minimizes:

$$
\sum \left| \text{expected chain weight}_i - \text{current chain weight}_i \right|
$$

Liquidity is bridged using intent based bridges like Across and Everclear for security purposes. Aside from bridging fees, points/token rewards will be used to create strong relationships with solvers.

In case of a token providing an official burn-and-mint bridge, Malda can make use of it for protocol health. The reason for this is in case of a bug and exploit in the burn-and-mint bridge the underlying token’s value will drop, which means that the protocol does not take on additional trust assumptions.
