# Introduction

_Malda is a Unified Liquidity Lending protocol on Ethereum and Layer 2s, delivering a seamless lending experience through global liquidity pools, all secured by zkProofs._

Malda is the first DeFi protocol built fully on a zkCoprocessor technology to create unified pools within the wider Ethereum ecosystem that act in sync to provide uninterrupted money market services solving the problem of liquidity fragmentation.

Over the next year Malda will be expanded into the next-gen lending protocol that will use the power of zero-knowledge proofs to provide unparalleled services via off-chain computations, but remain fully verifiable. We firmly believe that future applications will only use blockchains as an anchor for verified state but all computation will happen off-chain.

The next step in building the first truly scalable money market DeFi protocol is to outsource all the interest rate and balance calculations into a zkVM. This will enable us to do two major things:

* Fully customizable interest rate curves for users
* Scaling Ethereum
  * Our theoretical throughput can be calculated in the following way:
    * Currently: Mainnet TPS → L2 TPS → x100 via zkVM → Malda throughput
    * Changes introduced to each of the above component will increase Malda throughput exponentially

<figure><img src="https://799135553-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FGI1QYP4cTEZrQ1wnsNvu%2Fuploads%2FHwpgsqglTCtNuliRACMl%2FScalability%20and%20Throughput.png?alt=media&#x26;token=ab5c7407-2634-4083-ade6-42eb655ce311" alt=""><figcaption></figcaption></figure>
