# Use Cases

Malda has multiple new use cases and advantages compared to traditional architecture:

* Ability to use 1 (!) money market as a DeFi bank to manage all investments through the major rollups of the Ethereum ecosystem
* Seamless arbitrage across chains with borrowed funds between DEXs
* Easy and convenient user-experience without needing to disrupt user flow to bridge between chains.
* Quick access to investment opportunity with the ability to borrow or withdraw on supported chains instantaneously.&#x20;
* Easy migration and access for Ethereum Mainnet liquidity to major rollups
* Scale and Depth - Currently legacy lending models suffer fragmented liquidity and it is dependent on the underlying chain the quality of the service they can provide. Malda removes this limitation and provides the full depth of liquidity to all served markets, increasing capital efficiency and fostering meaningful economic growth in the Ethereum ecosystem.
