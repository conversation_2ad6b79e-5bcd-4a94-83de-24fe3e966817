# Architecture

#### ZK Coprocessor

<figure><img src="https://799135553-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FGI1QYP4cTEZrQ1wnsNvu%2Fuploads%2FsdcLSlBysG9FJf2Dyrdz%2Fimage.png?alt=media&#x26;token=84ad116c-4309-49c4-9b1c-cfb935804f52" alt=""><figcaption></figcaption></figure>

The core component that enables trustless cross-chain operations. It:

* Generates zero-knowledge proofs for cross-chain state verification
* Provides reorg protection for all supported chains
* Enables trustless cross-chain communication without bridges

#### Other Protocol Components

The ZK coprocessor works alongside the protocol's smart contracts, which handle lending operations and state management, and the Sequencer infrastructure, which monitors events and submits proofs. Despite being centralized, the Sequencer is constrained by ZK proofs and cannot manipulate user funds, ensuring the protocol remains self-custodial. For censorship resistance, users can [generate their own proofs](https://github.com/malda-protocol/malda-zk-coprocessor/tree/minimalDocs#self-sequencing) if needed.
