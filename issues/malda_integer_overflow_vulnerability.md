# Malda Protocol - Integer Overflow Bug

## Description

Integer overflow vulnerability in `BatchSubmitter.batchProcess()` function allows attackers with `PROOF_FORWARDER` role to access arbitrary journal entries through array index wrap-around, similar to the Memory Allocator vulnerability pattern.

## Vulnerability Details

**Location**: `malda-lending/src/mToken/BatchSubmitter.sol:115`

**Vulnerable Code**:
```solidity
function batchProcess(BatchProcessMsg calldata data) external {
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.PROOF_FORWARDER())) {
        revert BatchSubmitter_CallerNotAllowed();
    }

    bytes[] memory journals = abi.decode(data.journalData, (bytes[]));
    uint256 length = data.initHashes.length;

    for (uint256 i = 0; i < length;) {
        bytes[] memory singleJournal = new bytes[](1);
        singleJournal[0] = journals[data.startIndex + i];  // ❌ VULNERABILITY
        //                         ^^^^^^^^^^^^^^^^^^^
        //                         No overflow check!

        unchecked { ++i; }
    }
}
```

**Root Cause**:
1. `data.startIndex` is user-controlled input without validation
2. `data.startIndex + i` can overflow and wrap around to small values
3. No bounds checking against `journals.length`
4. Allows access to arbitrary array elements through integer overflow

**Attack Vector**:
- Attacker with `PROOF_FORWARDER` role sets `startIndex = uint256.max - N`
- During loop iteration, `startIndex + i` overflows and wraps to beginning of array
- Enables reading journals intended for other users/operations

**Proof of Concept**:
```solidity
// Attack scenario:
BatchProcessMsg({
    startIndex: 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB, // uint256.max - 4
    initHashes: [hash1, hash2, hash3, hash4, hash5, hash6, hash7, hash8],  // 8 elements
    journalData: abi.encode([journal0, journal1, journal2, journal3]),     // 4 journals
    // ... other fields
})

// Loop execution:
// i=0: journals[uint256.max-4 + 0] = journals[uint256.max-4] ✓ (if exists)
// i=1: journals[uint256.max-4 + 1] = journals[uint256.max-3] ✓
// i=2: journals[uint256.max-4 + 2] = journals[uint256.max-2] ✓
// i=3: journals[uint256.max-4 + 3] = journals[uint256.max-1] ✓
// i=4: journals[uint256.max-4 + 4] = journals[uint256.max] ✓
// i=5: journals[uint256.max-4 + 5] = journals[0] ❌ OVERFLOW! Wrap to start
// i=6: journals[uint256.max-4 + 6] = journals[1] ❌ OVERFLOW!
// i=7: journals[uint256.max-4 + 7] = journals[2] ❌ OVERFLOW!
```

**Analogy to Memory Allocator Bug**:
- **Memory Allocator**: `heap_pos += bytes` without overflow check → access to foreign memory
- **BatchSubmitter**: `startIndex + i` without overflow check → access to foreign journals
- **Common Pattern**: Integer overflow bypasses protections through wrap-around effect

## Impact

**Severity**: HIGH

**Primary Impacts**:
1. **Cross-User Data Access**: Attackers can read and process journal entries belonging to other users
2. **Unauthorized Operations**: Execution of `mintExternal()`, `repayExternal()`, or `outHere()` with victim's journal data
3. **Privilege Escalation**: Bypass of intended access controls through journal manipulation
4. **Protocol Integrity Violation**: Operations executed with unintended data, breaking protocol assumptions

**Attack Scenarios**:

1. **Cross-Journal Data Leakage**:
   ```solidity
   // journals = [attacker_journal, victim_journal_1, victim_journal_2, admin_journal]
   // startIndex = uint256.max - 2, initHashes.length = 6

   // Overflow result:
   // journals[uint256.max-2 + 3] = journals[0] = attacker_journal ✓
   // journals[uint256.max-2 + 4] = journals[1] = victim_journal_1 ❌
   // journals[uint256.max-2 + 5] = journals[2] = victim_journal_2 ❌
   // Attacker gains access to victim data!
   ```

2. **Unauthorized Token Operations**:
   - Mint tokens using victim's deposit proofs
   - Execute repayments with wrong user data
   - Withdraw funds to attacker's address using victim's authorization

**Prerequisites**:
- Attacker must have `PROOF_FORWARDER` role
- Access to valid journal data structure
- Knowledge of target journal positions in array

**CVSS Score**: 7.5+ (High) - Requires privileged access but enables significant unauthorized operations

## Recommended Fix

```solidity
function batchProcess(BatchProcessMsg calldata data) external {
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.PROOF_FORWARDER())) {
        revert BatchSubmitter_CallerNotAllowed();
    }

    _verifyProof(data.journalData, data.seal);
    bytes[] memory journals = abi.decode(data.journalData, (bytes[]));
    uint256 length = data.initHashes.length;

    // ✅ ADD BOUNDS CHECKS:
    require(data.startIndex < journals.length, "Invalid startIndex");
    require(data.startIndex <= type(uint256).max - length, "Addition overflow");
    require(data.startIndex + length <= journals.length, "Insufficient journals");

    for (uint256 i = 0; i < length;) {
        bytes[] memory singleJournal = new bytes[](1);
        singleJournal[0] = journals[data.startIndex + i];  // ✅ Now safe
        // ... rest of logic

        unchecked { ++i; }
    }
}
```

## Additional Context

**Access Control**:
- Only `PROOF_FORWARDER` role can call `batchProcess()`
- Current role holders: Admin (`0xCde13fF278bc484a09aDb69ea1eEd3cAf6Ea4E00`) and Sequencer (`0x2693946791da99dA78Ac441abA6D5Ce2Bccd96D3`)

**Exploitation Vectors**:
1. Compromised `PROOF_FORWARDER` account
2. Bug in off-chain Sequencer code
3. Race conditions in batch processing logic

This vulnerability demonstrates the importance of bounds checking in arithmetic operations over indices, especially when indices come from external sources. The analogy with Memory Allocator vulnerability shows that similar issues can occur at different abstraction levels - from system-level memory management to application-level array indexing.
