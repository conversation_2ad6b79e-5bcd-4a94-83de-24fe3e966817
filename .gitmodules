[submodule "malda-lending/lib/forge-std"]
	path = malda-lending/lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "malda-lending/lib/openzeppelin-foundry-upgrades"]
	path = malda-lending/lib/openzeppelin-foundry-upgrades
	url = https://github.com/OpenZeppelin/openzeppelin-foundry-upgrades
[submodule "malda-lending/lib/openzeppelin-contracts-upgradeable"]
	path = malda-lending/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "malda-lending/lib/openzeppelin-contracts"]
	path = malda-lending/lib/openzeppelin-contracts
	url = https://github.com/OpenZeppelin/openzeppelin-contracts
[submodule "malda-lending/lib/risc0-ethereum"]
	path = malda-lending/lib/risc0-ethereum
	url = https://github.com/risc0/risc0-ethereum
