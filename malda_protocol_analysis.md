# Анализ протокола Malda

## 1. Ключевые пользовательские сценарии

### 1.1 Основные операции (Flow'ы)

#### **Supply (Deposit) - Депозит активов**

**Host Chain (Linea) - Прямые операции:**
```solidity
// Прямой вызов на Host chain
function __mint(uint256 mintAmount, address receiver, uint256 minAmountOut) external
```

**Extension Chain - Кросс-чейн операции:**
```solidity
// 1. Пользователь вызывает на Extension chain
function supplyOnHost(uint256 amount, address receiver, bytes4 lineaSelector) external payable
// 2. Sequencer генерирует zkProof
// 3. Выполняется на Host chain
function mintExternal(bytes calldata journalData, bytes calldata seal, uint256[] calldata mintAmount, uint256[] calldata minAmountsOut, address receiver) external
```

#### **Withdraw - Вывод активов**

**Host Chain:**
```solidity
function __redeem(uint256 redeemTokens, address receiver, uint256 minAmountOut) external
```

**Extension Chain:**
```solidity
// 1. Генерируется zkProof на Host chain
// 2. Выполняется на Extension chain
function outHere(bytes calldata journalData, bytes calldata seal, uint256[] calldata amounts, address receiver) external
```

#### **Borrow - Заимствование**

**Host Chain:**
```solidity
function __borrow(uint256 borrowAmount, address receiver) external
```

**Extension Chain:**
```solidity
// Аналогично withdraw - требует zkProof с Host chain
function outHere(bytes calldata journalData, bytes calldata seal, uint256[] calldata amounts, address receiver) external
```

#### **Repay - Погашение долга**

**Host Chain:**
```solidity
function __repayBorrow(uint256 repayAmount, address borrower) external
```

**Extension Chain:**
```solidity
// Аналогично supply - генерирует zkProof для Host chain
function supplyOnHost(uint256 amount, address receiver, bytes4 lineaSelector) external payable
// Затем на Host chain
function repayExternal(bytes calldata journalData, bytes calldata seal, uint256[] calldata repayAmount, address receiver) external
```

### 1.2 Типы транзакций по архитектуре

1. **Linea native transactions** - обычные операции как в legacy lending протоколах
2. **Deposit type transactions** (Supply, Repay) - от Extension к Host с zkProof верификацией
3. **Withdraw type transactions** (Withdraw, Borrow) - от Host к Extension с zkProof авторизацией

## 2. Точки входа (Entry Points)

### 2.1 Host Chain (Linea) - mErc20Host.sol

**Публичные функции для пользователей:**
```solidity
// Прямые операции
function __mint(uint256 mintAmount, address receiver, uint256 minAmountOut) external
function __redeem(uint256 redeemTokens, address receiver, uint256 minAmountOut) external  
function __borrow(uint256 borrowAmount, address receiver) external
function __repayBorrow(uint256 repayAmount, address borrower) external

// Кросс-чейн операции с zkProof
function mintExternal(bytes calldata journalData, bytes calldata seal, uint256[] calldata mintAmount, uint256[] calldata minAmountsOut, address receiver) external
function repayExternal(bytes calldata journalData, bytes calldata seal, uint256[] calldata repayAmount, address receiver) external
```

### 2.2 Extension Chains - mTokenGateway.sol

**Публичные функции для пользователей:**
```solidity
// Депозит операции (генерируют zkProof для Host)
function supplyOnHost(uint256 amount, address receiver, bytes4 lineaSelector) external payable

// Вывод операции (требуют zkProof с Host)
function outHere(bytes calldata journalData, bytes calldata seal, uint256[] calldata amounts, address receiver) external
```

### 2.3 Batch операции - BatchSubmitter.sol

**Для массовых операций:**
```solidity
function batchProcess(BatchProcessMsg calldata data) external
```

## 3. Архитектура Host vs Extension

### 3.1 Host Chain (Linea) - Центральный учет

**Роль и ответственности:**
- **Полный учет (Full Accounting)**: Хранит все балансы, долги, проценты
- **Финальный ledger**: Окончательный источник истины для всех позиций
- **Interest Rate Calculation**: Глобальные процентные ставки на основе общей утилизации
- **Liquidation Logic**: Обработка ликвидаций
- **Oracle Integration**: Получение цен активов

**Ключевые компоненты:**
```solidity
contract mErc20Host {
    // Основная логика lending протокола
    // Хранение балансов и долгов
    // Расчет процентов
    // Проверки collateral
}
```

### 3.2 Extension Chains - Локальные пулы

**Роль и ответственности:**
- **Локальное хранение активов**: Физические токены находятся на Extension chains
- **Entry Points**: Точки входа для пользователей на разных чейнах
- **zkProof Generation**: Генерация доказательств для операций
- **Gas Fee Collection**: Сбор комиссий за кросс-чейн операции

**Ключевые компоненты:**
```solidity
contract mTokenGateway {
    // Локальные пулы активов
    // Генерация events для zkProof
    // Верификация zkProof для выводов
}
```

### 3.3 Взаимодействие Host ↔ Extension

**Deposit Flow (Extension → Host):**
1. Пользователь вызывает `supplyOnHost()` на Extension
2. Токены блокируются в Extension контракте
3. Sequencer генерирует zkProof
4. `mintExternal()` выполняется на Host с proof верификацией
5. Балансы обновляются в Host accounting

**Withdraw Flow (Host → Extension):**
1. Генерируется zkProof на Host chain (подтверждение права на вывод)
2. `outHere()` вызывается на Extension с proof
3. Токены высвобождаются из Extension контракта
4. Балансы обновляются в Host accounting

## 4. Генерация zkProof

### 4.1 ZK Coprocessor Architecture

**Компоненты системы:**
- **RISC Zero zkVM**: Выполнение guest программ для генерации proof'ов
- **Guest Program**: `get_proof_data.rs` - основная логика верификации
- **Steel Framework**: Верификация состояния блокчейнов
- **Sequencer**: Централизованный сервис для автоматической генерации proof'ов

### 4.2 Функции генерации zkProof

**В Rust коде (malda-zk-coprocessor):**
```rust
// Основная функция генерации proof'ов
pub async fn get_proof_data_prove_sdk(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>, 
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
) -> Result<MaldaProveInfo, Error>

// Batch верификация для множественных операций
pub fn batch_call_get_proof_data<H>(
    chain_id: u64,
    account: Vec<Address>,
    asset: Vec<Address>, 
    target_chain_ids: Vec<u64>,
    env: EvmEnv<StateDb, H, Commitment>,
    validate_l1_inclusion: bool,
    output: &mut Vec<Bytes>,
)
```

### 4.3 Верификация zkProof в контрактах

**ZkVerifier.sol:**
```solidity
contract ZkVerifier {
    IRiscZeroVerifier public verifier; // RISC Zero верификатор
    bytes32 public imageId; // ID guest программы
    
    function verifyInput(bytes calldata journalEntry, bytes calldata seal) external view {
        verifier.verify(seal, imageId, sha256(journalEntry));
    }
}
```

**Использование в контрактах:**
```solidity
// В mErc20Host.sol
function mintExternal(...) external {
    if (!_isAllowedFor(msg.sender, _getBatchProofForwarderRole())) {
        _verifyProof(journalData, seal); // Верификация zkProof
    }
    // ... выполнение операции
}

// В mTokenGateway.sol  
function outHere(...) external {
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.PROOF_BATCH_FORWARDER())) {
        _verifyProof(journalData, seal); // Верификация zkProof
    }
    // ... выполнение операции
}
```

## 5. Критические точки безопасности (Sink Points)

### 5.1 Обход zkProof верификации

**PROOF_BATCH_FORWARDER роль:**
```solidity
// В mErc20Host.sol - КРИТИЧЕСКАЯ УЯЗВИМОСТЬ
function mintExternal(...) external {
    if (!_isAllowedFor(msg.sender, _getBatchProofForwarderRole())) {
        _verifyProof(journalData, seal);
    }
    // Если роль есть - zkProof НЕ ПРОВЕРЯЕТСЯ!
}
```

**Риски:**
- Скомпрометированная роль может создавать fake операции
- Минтинг токенов без реальных депозитов
- Вывод средств без авторизации

### 5.2 BatchSubmitter bypass

**В BatchSubmitter.sol:**
```solidity
function batchProcess(BatchProcessMsg calldata data) external {
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.PROOF_FORWARDER())) {
        revert BatchSubmitter_CallerNotAllowed();
    }
    // Вызывает mintExternal с пустым seal: ""
    ImErc20Host(data.mTokens[i]).mintExternal(encodedJournal, "", singleAmount, singleMinAmounts, data.receivers[i])
}
```

**Проблема:** BatchSubmitter автоматически получает PROOF_BATCH_FORWARDER роль при деплое, что позволяет обходить zkProof верификацию.

### 5.3 Self-Sequencing риски

**Пользователи могут генерировать собственные proof'ы:**
- Сложность настройки может привести к ошибкам
- Неправильная конфигурация `l1_inclusion` параметра
- Потенциальные reorg атаки при `l1_inclusion=false`

### 5.4 Cross-chain state inconsistency

**Потенциальные проблемы:**
- Рассинхронизация между Host и Extension chains
- Проблемы с rebalancing при недостатке ликвидности
- Временные окна между генерацией proof и его выполнением

### 5.5 Oracle и Price Feed риски

**Зависимости:**
- Централизованные oracle для цен активов
- Потенциальные манипуляции цен для ликвидаций
- Проблемы с stale prices при сетевых проблемах

## 6. Применение опыта CAP аудита - Критические уязвимости

### 6.1 Sink-Source анализ (по методологии Dravee)

**Критические Sink Points (где могут быть украдены активы):**

1. **mintExternal() с обходом zkProof**
2. **outHere() с поддельными proof'ами**
3. **BatchSubmitter с привилегированными ролями**
4. **Rebalancer с неограниченными правами**

### 6.2 Потенциальные атаки на основе CAP опыта

#### **Атака #1: PROOF_BATCH_FORWARDER Privilege Escalation**

<augment_code_snippet path="malda-lending/src/mToken/host/mErc20Host.sol" mode="EXCERPT">
````solidity
function mintExternal(...) external override {
    if (!_isAllowedFor(msg.sender, _getBatchProofForwarderRole())) {
        _verifyProof(journalData, seal);
    }
    // УЯЗВИМОСТЬ: Роль может минтить без proof'а
    _checkOutflow(CommonLib.computeSum(mintAmount));
````
</augment_code_snippet>

**Сценарий атаки:**
1. Компрометация PROOF_BATCH_FORWARDER роли
2. Прямой вызов `mintExternal()` с fake данными
3. Минтинг mTokens без реальных депозитов
4. Конвертация в underlying активы через `__redeem()`

#### **Атака #2: Cross-Chain State Manipulation**

**Проблема:** Рассинхронизация между Extension и Host chains

<augment_code_snippet path="malda-lending/src/mToken/extension/mTokenGateway.sol" mode="EXCERPT">
````solidity
function supplyOnHost(uint256 amount, address receiver, bytes4 lineaSelector) external payable {
    IERC20(underlying).safeTransferFrom(msg.sender, address(this), amount);
    accAmountIn[receiver] += amount; // Локальный учет
    // Но Host chain может не получить proof
````
</augment_code_snippet>

**Сценарий атаки:**
1. Депозит на Extension chain
2. Блокировка/цензура Sequencer'а
3. Активы заблокированы на Extension, но не учтены на Host
4. Потеря средств пользователей

#### **Атака #3: Rebalancer Manipulation**

**Аналогично CAP coverage=0 атаке:**

<augment_code_snippet path="malda-lending/src/rebalancer/Rebalancer.sol" mode="EXCERPT">
````solidity
// Rebalancer может перемещать активы между chains
// Потенциальная атака: искусственное создание дефицита ликвидности
````
</augment_code_snippet>

### 6.3 What was assumed but never enforced?

#### **Предположение 1:** Sequencer всегда доступен
- **Реальность:** Может быть недоступен или скомпрометирован
- **Последствия:** Блокировка кросс-чейн операций

#### **Предположение 2:** zkProof всегда валидны
- **Реальность:** PROOF_BATCH_FORWARDER может их обходить
- **Последствия:** Fake операции без верификации

#### **Предположение 3:** Роли правильно настроены
- **Реальность:** BatchSubmitter автоматически получает критические права
- **Последствия:** Расширенная поверхность атаки

### 6.4 Рекомендации на основе CAP опыта

1. **Ограничить PROOF_BATCH_FORWARDER роль**
   - Добавить временные ограничения
   - Требовать multi-sig для критических операций

2. **Улучшить мониторинг состояния**
   - Проверки консистентности между chains
   - Алерты при рассинхронизации

3. **Защита от Sequencer failure**
   - Автоматический fallback на self-sequencing
   - Timeout механизмы для stuck операций

4. **Аудит ролей и разрешений**
   - Принцип минимальных привилегий
   - Регулярная ротация критических ключей

Этот анализ показывает, что протокол Malda имеет сложную архитектуру с несколькими критическими точками, где неправильная конфигурация ролей или компрометация привилегированных аккаунтов может привести к серьезным уязвимостям, аналогичным найденным в CAP протоколе.
